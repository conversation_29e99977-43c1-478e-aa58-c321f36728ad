// Core types for WeedNearMeDC platform

export interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  phone?: string
  dateOfBirth?: Date
  ageVerified: boolean
  idVerified: boolean
  state?: 'DC' | 'MD' | 'VA'
  city?: string
  zipCode?: string
  address?: string
  role: 'CUSTOMER' | 'DRIVER' | 'ADMIN' | 'SUPER_ADMIN'
  createdAt: Date
  updatedAt: Date
}

export interface Product {
  id: number
  name: string
  slug: string
  description?: string
  category: ProductCategory
  subcategory?: string
  thcContent?: string
  cbdContent?: string
  strain?: string
  effects: string[]
  flavors: string[]
  price: number
  compareAtPrice?: number
  weight?: string
  inStock: boolean
  stockCount: number
  featured: boolean
  onSale: boolean
  images: string[]
  videoUrl?: string
  rating: number
  reviewCount: number
  labTested: boolean
  labResults?: string
  availableInDC: boolean
  availableInMD: boolean
  availableInVA: boolean
  createdAt: Date
  updatedAt: Date
}

export type ProductCategory = 
  | 'FLOWER'
  | 'EDIBLES' 
  | 'VAPES'
  | 'CONCENTRATES'
  | 'TOPICALS'
  | 'ACCESSORIES'
  | 'PRE_ROLLS'
  | 'TINCTURES'

export interface CartItem {
  id: number
  userId: string
  productId: number
  quantity: number
  product: Product
  createdAt: Date
  updatedAt: Date
}

export interface Order {
  id: number
  userId: string
  orderNumber: string
  status: OrderStatus
  total: number
  subtotal: number
  tax: number
  tip: number
  deliveryFee: number
  deliveryAddress: string
  deliveryCity: string
  deliveryState: string
  deliveryZip: string
  deliveryNotes?: string
  driverId?: string
  estimatedDelivery?: Date
  actualDelivery?: Date
  paymentMethod?: string
  paymentStatus: PaymentStatus
  createdAt: Date
  updatedAt: Date
  items: OrderItem[]
}

export type OrderStatus = 
  | 'PENDING'
  | 'CONFIRMED'
  | 'PREPARING'
  | 'OUT_FOR_DELIVERY'
  | 'DELIVERED'
  | 'CANCELLED'
  | 'REFUNDED'

export type PaymentStatus = 
  | 'PENDING'
  | 'PROCESSING'
  | 'COMPLETED'
  | 'FAILED'
  | 'REFUNDED'

export interface OrderItem {
  id: number
  orderId: number
  productId: number
  quantity: number
  price: number
  product: Product
}

export interface Review {
  id: number
  userId: string
  productId: number
  rating: number
  title?: string
  body?: string
  verified: boolean
  helpful: number
  createdAt: Date
  updatedAt: Date
  user: User
}

export interface Loyalty {
  userId: string
  points: number
  orders: number
  tier: LoyaltyTier
  badges: string[]
  strainsDiscovered: number
  categoriesExplored: number
  greenhouseLevel: number
  plantsOwned: string[]
  referralCode: string
  referralsCount: number
  createdAt: Date
  updatedAt: Date
}

export type LoyaltyTier = 
  | 'BRONZE'
  | 'SILVER'
  | 'GOLD'
  | 'PLATINUM'
  | 'DIAMOND'

// State-specific types
export type StateCode = 'DC' | 'MD' | 'VA'

export interface StateInfo {
  code: StateCode
  name: string
  legalStatus: 'recreational' | 'medical' | 'decriminalized'
  deliveryAvailable: boolean
  ageRequirement: number
  possessionLimits: {
    flower: string
    edibles: string
    concentrates: string
  }
}

// Cart & Shopping types
export interface CartState {
  items: CartItem[]
  total: number
  itemCount: number
  isLoading: boolean
}

// API Response types
export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
  success: boolean
}

// Search & Filter types
export interface ProductFilters {
  category?: ProductCategory
  minPrice?: number
  maxPrice?: number
  strain?: string
  effects?: string[]
  inStock?: boolean
  onSale?: boolean
  featured?: boolean
}

export interface SearchParams {
  query?: string
  category?: ProductCategory
  page?: number
  limit?: number
  sortBy?: 'name' | 'price' | 'rating' | 'created'
  sortOrder?: 'asc' | 'desc'
  filters?: ProductFilters
}
