import { StateInfo, ProductCategory } from '@/types'

// State information for DC, MD, VA
export const STATES: Record<string, StateInfo> = {
  DC: {
    code: 'DC',
    name: 'Washington, D.C.',
    legalStatus: 'recreational',
    deliveryAvailable: false, // Informational only
    ageRequirement: 21,
    possessionLimits: {
      flower: '2 ounces',
      edibles: 'No specific limit',
      concentrates: 'No specific limit'
    }
  },
  MD: {
    code: 'MD',
    name: 'Maryland',
    legalStatus: 'recreational',
    deliveryAvailable: true, // Primary market
    ageRequirement: 21,
    possessionLimits: {
      flower: '1.5 ounces',
      edibles: 'No specific limit',
      concentrates: '12 grams'
    }
  },
  VA: {
    code: 'VA',
    name: 'Virginia',
    legalStatus: 'recreational',
    deliveryAvailable: false, // Informational only
    ageRequirement: 21,
    possessionLimits: {
      flower: '1 ounce',
      edibles: 'No specific limit',
      concentrates: 'No specific limit'
    }
  }
}

// Product categories with display information
export const PRODUCT_CATEGORIES: Record<ProductCategory, {
  label: string
  description: string
  icon: string
  slug: string
}> = {
  FLOWER: {
    label: 'Flower',
    description: 'Premium cannabis flower in various strains',
    icon: '🌸',
    slug: 'flower'
  },
  EDIBLES: {
    label: 'Edibles',
    description: 'Cannabis-infused foods and beverages',
    icon: '🍪',
    slug: 'edibles'
  },
  VAPES: {
    label: 'Vapes',
    description: 'Vape cartridges and disposable pens',
    icon: '💨',
    slug: 'vapes'
  },
  CONCENTRATES: {
    label: 'Concentrates',
    description: 'High-potency cannabis extracts',
    icon: '💎',
    slug: 'concentrates'
  },
  TOPICALS: {
    label: 'Topicals',
    description: 'Cannabis-infused creams and balms',
    icon: '🧴',
    slug: 'topicals'
  },
  ACCESSORIES: {
    label: 'Accessories',
    description: 'Smoking and vaping accessories',
    icon: '🔧',
    slug: 'accessories'
  },
  PRE_ROLLS: {
    label: 'Pre-Rolls',
    description: 'Ready-to-smoke cannabis joints',
    icon: '🚬',
    slug: 'pre-rolls'
  },
  TINCTURES: {
    label: 'Tinctures',
    description: 'Liquid cannabis extracts',
    icon: '💧',
    slug: 'tinctures'
  }
}

// Cannabis strains
export const STRAIN_TYPES = {
  INDICA: 'Indica',
  SATIVA: 'Sativa',
  HYBRID: 'Hybrid'
}

// Common cannabis effects
export const CANNABIS_EFFECTS = [
  'Relaxing',
  'Euphoric',
  'Creative',
  'Energizing',
  'Focused',
  'Happy',
  'Sleepy',
  'Uplifting',
  'Calming',
  'Giggly'
]

// Common cannabis flavors
export const CANNABIS_FLAVORS = [
  'Citrus',
  'Earthy',
  'Sweet',
  'Pine',
  'Berry',
  'Diesel',
  'Floral',
  'Fruity',
  'Herbal',
  'Mint',
  'Spicy',
  'Vanilla'
]

// Loyalty tiers with benefits
export const LOYALTY_TIERS = {
  BRONZE: {
    name: 'Bronze',
    minOrders: 0,
    pointsMultiplier: 1,
    benefits: ['Basic rewards', 'Birthday discount']
  },
  SILVER: {
    name: 'Silver',
    minOrders: 5,
    pointsMultiplier: 1.25,
    benefits: ['25% bonus points', 'Early access to sales', 'Free delivery on orders $75+']
  },
  GOLD: {
    name: 'Gold',
    minOrders: 15,
    pointsMultiplier: 1.5,
    benefits: ['50% bonus points', 'Exclusive products', 'Free delivery on orders $50+']
  },
  PLATINUM: {
    name: 'Platinum',
    minOrders: 30,
    pointsMultiplier: 1.75,
    benefits: ['75% bonus points', 'VIP support', 'Free delivery on all orders']
  },
  DIAMOND: {
    name: 'Diamond',
    minOrders: 50,
    pointsMultiplier: 2,
    benefits: ['Double points', 'Concierge service', 'Exclusive events']
  }
}

// Order status display information
export const ORDER_STATUS_INFO = {
  PENDING: {
    label: 'Pending',
    description: 'Order received and being processed',
    color: 'yellow'
  },
  CONFIRMED: {
    label: 'Confirmed',
    description: 'Order confirmed and being prepared',
    color: 'blue'
  },
  PREPARING: {
    label: 'Preparing',
    description: 'Your order is being prepared',
    color: 'orange'
  },
  OUT_FOR_DELIVERY: {
    label: 'Out for Delivery',
    description: 'Driver is on the way',
    color: 'purple'
  },
  DELIVERED: {
    label: 'Delivered',
    description: 'Order successfully delivered',
    color: 'green'
  },
  CANCELLED: {
    label: 'Cancelled',
    description: 'Order was cancelled',
    color: 'red'
  },
  REFUNDED: {
    label: 'Refunded',
    description: 'Order was refunded',
    color: 'gray'
  }
}

// App configuration
export const APP_CONFIG = {
  name: 'WeedNearMeDC',
  description: 'Premium cannabis delivery in DC, Maryland & Virginia',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  minAge: 21,
  deliveryFee: 500, // $5.00 in cents
  freeDeliveryThreshold: 7500, // $75.00 in cents
  maxDeliveryDistance: 25, // miles
  businessHours: {
    start: '09:00',
    end: '22:00'
  },
  supportEmail: '<EMAIL>',
  supportPhone: '(202) 555-WEED'
}

// SEO constants
export const SEO_DEFAULTS = {
  title: 'WeedNearMeDC - Premium Cannabis Delivery',
  description: 'Fast, reliable cannabis delivery in Washington DC, Maryland & Virginia. Premium flower, edibles, vapes & more. Order online for same-day delivery.',
  keywords: 'cannabis delivery, weed delivery, marijuana delivery, DC, Maryland, Virginia, dispensary',
  ogImage: '/images/og-image.jpg'
}
