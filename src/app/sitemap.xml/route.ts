import { APP_CONFIG, PRODUCT_CATEGORIES } from '@/constants'

// Mock product data for sitemap generation
// In production, this would fetch from your database
const mockProducts = [
  { slug: 'blue-dream', category: 'flower', updatedAt: new Date('2024-01-15') },
  { slug: 'og-kush', category: 'flower', updatedAt: new Date('2024-01-14') },
  { slug: 'sour-diesel-gummies', category: 'edibles', updatedAt: new Date('2024-01-13') },
  { slug: 'live-resin-vape-cart-wedding-cake', category: 'vapes', updatedAt: new Date('2024-01-12') },
  { slug: 'rosin-concentrate-gelato', category: 'concentrates', updatedAt: new Date('2024-01-11') },
  { slug: 'cbd-relief-balm', category: 'topicals', updatedAt: new Date('2024-01-10') },
  { slug: 'pre-roll-5-pack-mixed-strains', category: 'pre-rolls', updatedAt: new Date('2024-01-09') },
  { slug: 'full-spectrum-tincture-1000mg', category: 'tinctures', updatedAt: new Date('2024-01-08') }
]

function generateSitemap() {
  const baseUrl = APP_CONFIG.url
  const currentDate = new Date().toISOString()

  // Static pages
  const staticPages = [
    { url: '', priority: '1.0', changefreq: 'daily' },
    { url: '/md', priority: '0.9', changefreq: 'daily' },
    { url: '/dc', priority: '0.8', changefreq: 'weekly' },
    { url: '/va', priority: '0.8', changefreq: 'weekly' },
    { url: '/about', priority: '0.7', changefreq: 'monthly' },
    { url: '/contact', priority: '0.7', changefreq: 'monthly' },
    { url: '/faq', priority: '0.6', changefreq: 'monthly' },
    { url: '/terms', priority: '0.5', changefreq: 'yearly' },
    { url: '/privacy', priority: '0.5', changefreq: 'yearly' },
    { url: '/shipping', priority: '0.5', changefreq: 'monthly' },
    { url: '/refund', priority: '0.5', changefreq: 'monthly' },
    { url: '/compliance', priority: '0.5', changefreq: 'monthly' },
    { url: '/auth/login', priority: '0.6', changefreq: 'monthly' },
    { url: '/auth/register', priority: '0.6', changefreq: 'monthly' },
    { url: '/cart', priority: '0.7', changefreq: 'daily' },
    { url: '/checkout', priority: '0.7', changefreq: 'daily' }
  ]

  // Category pages
  const categoryPages = Object.entries(PRODUCT_CATEGORIES).map(([key, category]) => ({
    url: `/md/products/${category.slug}`,
    priority: '0.8',
    changefreq: 'daily'
  }))

  // Product pages
  const productPages = mockProducts.map(product => ({
    url: `/md/products/${PRODUCT_CATEGORIES[product.category.toUpperCase() as keyof typeof PRODUCT_CATEGORIES]?.slug}/${product.slug}`,
    priority: '0.7',
    changefreq: 'weekly',
    lastmod: product.updatedAt.toISOString()
  }))

  // Combine all pages
  const allPages = [...staticPages, ...categoryPages, ...productPages]

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${page.lastmod || currentDate}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`

  return sitemap
}

export async function GET() {
  const sitemap = generateSitemap()

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600' // Cache for 1 hour
    }
  })
}
