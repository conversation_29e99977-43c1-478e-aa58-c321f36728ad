"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  Package, 
  Truck, 
  Clock, 
  Phone, 
  MessageSquare,
  Home,
  Receipt
} from "lucide-react"
import Link from "next/link"

export default function CheckoutSuccessPage() {
  const searchParams = useSearchParams()
  const orderNumber = searchParams.get('order') || 'WND-2024-001'
  const [estimatedDelivery, setEstimatedDelivery] = useState<Date>()

  useEffect(() => {
    // Calculate estimated delivery time (2-4 hours from now)
    const now = new Date()
    const deliveryTime = new Date(now.getTime() + (2.5 * 60 * 60 * 1000)) // 2.5 hours
    setEstimatedDelivery(deliveryTime)
  }, [])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            {/* Success Header */}
            <Card className="mb-8">
              <CardContent className="text-center py-12">
                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="h-12 w-12 text-green-600" />
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  Order Confirmed!
                </h1>
                <p className="text-xl text-gray-600 mb-6">
                  Thank you for your order. We're preparing your cannabis delivery.
                </p>
                <div className="flex items-center justify-center gap-2 mb-6">
                  <span className="text-gray-600">Order Number:</span>
                  <Badge variant="outline" className="text-lg px-3 py-1">
                    {orderNumber}
                  </Badge>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild className="bg-green-600 hover:bg-green-700">
                    <Link href={`/track/${orderNumber.toLowerCase().replace('#', '')}`}>
                      <Package className="h-4 w-4 mr-2" />
                      Track Your Order
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/md">
                      <Home className="h-4 w-4 mr-2" />
                      Continue Shopping
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Order Status */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5 text-green-600" />
                  Delivery Information
                </CardTitle>
                <CardDescription>
                  Your order is being prepared for delivery
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <div className="font-medium text-green-800">Order Received</div>
                      <div className="text-sm text-green-600">
                        We've received your order and payment
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <Package className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium text-blue-800">Preparing Your Order</div>
                      <div className="text-sm text-blue-600">
                        Our team is carefully preparing your items
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <Truck className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">Out for Delivery</div>
                      <div className="text-sm text-gray-600">
                        Your order will be on its way soon
                      </div>
                    </div>
                  </div>

                  {estimatedDelivery && (
                    <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                      <div className="flex items-center gap-2 text-amber-800">
                        <Clock className="h-5 w-5" />
                        <span className="font-medium">
                          Estimated Delivery: {formatTime(estimatedDelivery)}
                        </span>
                      </div>
                      <p className="text-sm text-amber-700 mt-1">
                        We'll send you updates via SMS and email as your order progresses.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* What's Next */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>What Happens Next?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-xs font-bold text-green-600">1</span>
                    </div>
                    <div>
                      <div className="font-medium">Order Confirmation</div>
                      <div className="text-sm text-gray-600">
                        You'll receive an email confirmation with your order details
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-xs font-bold text-green-600">2</span>
                    </div>
                    <div>
                      <div className="font-medium">Preparation</div>
                      <div className="text-sm text-gray-600">
                        Our team will carefully prepare and package your order
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-xs font-bold text-green-600">3</span>
                    </div>
                    <div>
                      <div className="font-medium">Driver Assignment</div>
                      <div className="text-sm text-gray-600">
                        A professional driver will be assigned to your delivery
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-xs font-bold text-green-600">4</span>
                    </div>
                    <div>
                      <div className="font-medium">Real-time Tracking</div>
                      <div className="text-sm text-gray-600">
                        Track your driver's location in real-time once they're on the way
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                      <span className="text-xs font-bold text-green-600">5</span>
                    </div>
                    <div>
                      <div className="font-medium">Delivery</div>
                      <div className="text-sm text-gray-600">
                        Your driver will call/text upon arrival. Have your ID ready!
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Important Reminders */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Important Reminders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <span className="text-sm">
                      <strong>ID Required:</strong> Please have a valid government-issued ID ready for age verification
                    </span>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <span className="text-sm">
                      <strong>Discreet Packaging:</strong> Your order will arrive in unmarked, odor-proof packaging
                    </span>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <span className="text-sm">
                      <strong>Contact Info:</strong> Make sure your phone is available for delivery updates
                    </span>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <span className="text-sm">
                      <strong>Payment:</strong> If paying cash, please have exact change ready
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Support */}
            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
                <CardDescription>
                  Our support team is here to help with any questions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Button variant="outline" className="w-full" asChild>
                    <a href="tel:******-555-WEED">
                      <Phone className="h-4 w-4 mr-2" />
                      Call Support
                    </a>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <a href="sms:******-555-WEED">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Text Support
                    </a>
                  </Button>
                </div>
                <div className="mt-4 text-center">
                  <Button variant="link" asChild>
                    <Link href="/contact">
                      Contact Us
                    </Link>
                  </Button>
                  <span className="text-gray-400 mx-2">•</span>
                  <Button variant="link" asChild>
                    <Link href="/faq">
                      FAQ
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
