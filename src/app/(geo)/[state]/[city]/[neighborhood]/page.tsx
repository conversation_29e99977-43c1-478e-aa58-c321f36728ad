import { Metada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import { MapPin, Clock, Truck, Star, Package, Shield } from 'lucide-react';
import JsonLd from '@/components/seo/JsonLd';
import GeoAwareCTA from '@/components/seo/GeoAwareCTA';

interface PageProps {
  params: {
    state: string;
    city: string;
    neighborhood: string;
  };
}

// DMV area data structure
const DMV_AREAS = {
  dc: {
    name: 'Washington DC',
    legalStatus: 'medical',
    deliveryTime: '2-4 hours',
    minOrder: '$100',
    cities: {
      'washington': {
        name: 'Washington',
        zip: '20001',
        neighborhoods: {
          'georgetown': { name: 'Georgetown', zip: '20007', deliveryAvailable: true },
          'u-street': { name: 'U Street', zip: '20009', deliveryAvailable: true },
          'adams-morgan': { name: 'Adams Morgan', zip: '20009', deliveryAvailable: true },
          'capitol-hill': { name: 'Capitol Hill', zip: '20003', deliveryAvailable: true },
          'dupont-circle': { name: 'Dupont Circle', zip: '20036', deliveryAvailable: true },
          'foggy-bottom': { name: 'Foggy Bottom', zip: '20037', deliveryAvailable: true },
          'shaw': { name: 'Shaw', zip: '20001', deliveryAvailable: true },
          'chinatown': { name: 'Chinatown', zip: '20001', deliveryAvailable: true }
        }
      }
    }
  },
  maryland: {
    name: 'Maryland',
    legalStatus: 'recreational',
    deliveryTime: '3-5 hours',
    minOrder: '$75',
    cities: {
      'baltimore': {
        name: 'Baltimore',
        zip: '21201',
        neighborhoods: {
          'inner-harbor': { name: 'Inner Harbor', zip: '21202', deliveryAvailable: true },
          'federal-hill': { name: 'Federal Hill', zip: '21230', deliveryAvailable: true },
          'fells-point': { name: 'Fells Point', zip: '21231', deliveryAvailable: true },
          'canton': { name: 'Canton', zip: '21224', deliveryAvailable: true }
        }
      },
      'silver-spring': {
        name: 'Silver Spring',
        zip: '20901',
        neighborhoods: {
          'downtown': { name: 'Downtown Silver Spring', zip: '20910', deliveryAvailable: true },
          'takoma-park': { name: 'Takoma Park', zip: '20912', deliveryAvailable: true }
        }
      },
      'rockville': {
        name: 'Rockville',
        zip: '20850',
        neighborhoods: {
          'town-center': { name: 'Town Center', zip: '20850', deliveryAvailable: true },
          'king-farm': { name: 'King Farm', zip: '20855', deliveryAvailable: true }
        }
      }
    }
  },
  virginia: {
    name: 'Virginia',
    legalStatus: 'medical',
    deliveryTime: 'Next day',
    minOrder: '$150',
    cities: {
      'richmond': {
        name: 'Richmond',
        zip: '23219',
        neighborhoods: {
          'downtown': { name: 'Downtown Richmond', zip: '23219', deliveryAvailable: true },
          'fan-district': { name: 'The Fan', zip: '23220', deliveryAvailable: true }
        }
      },
      'alexandria': {
        name: 'Alexandria',
        zip: '22301',
        neighborhoods: {
          'old-town': { name: 'Old Town', zip: '22314', deliveryAvailable: true },
          'del-ray': { name: 'Del Ray', zip: '22301', deliveryAvailable: true }
        }
      }
    }
  }
};

// Generate static paths for all geo combinations
export async function generateStaticParams() {
  const paths: { state: string; city: string; neighborhood: string }[] = [];
  
  Object.entries(DMV_AREAS).forEach(([stateKey, stateData]) => {
    Object.entries(stateData.cities).forEach(([cityKey, cityData]) => {
      Object.entries(cityData.neighborhoods).forEach(([neighborhoodKey]) => {
        paths.push({
          state: stateKey,
          city: cityKey,
          neighborhood: neighborhoodKey
        });
      });
    });
  });

  return paths;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { state, city, neighborhood } = params;
  
  const stateData = DMV_AREAS[state as keyof typeof DMV_AREAS];
  const cityData = stateData?.cities[city];
  const neighborhoodData = cityData?.neighborhoods[neighborhood];
  
  if (!stateData || !cityData || !neighborhoodData) {
    return {
      title: 'Page Not Found | WeedNearMeDC'
    };
  }

  const areaName = neighborhoodData.name;
  const cityName = cityData.name;
  const stateName = stateData.name;
  const legalType = stateData.legalStatus === 'recreational' ? 'Recreational' : 'Medical';
  
  const title = `${legalType} Cannabis Delivery in ${areaName}, ${cityName} | WeedNearMeDC`;
  const description = `Premium cannabis delivery to ${areaName}, ${cityName}, ${stateName}. ${stateData.deliveryTime} delivery • ${stateData.minOrder} minimum • Lab-tested products • Licensed dispensary.`;

  return {
    title,
    description,
    keywords: [
      `cannabis delivery ${areaName}`,
      `weed delivery ${cityName}`,
      `${legalType.toLowerCase()} marijuana ${stateName}`,
      `dispensary near ${areaName}`,
      `cannabis ${neighborhoodData.zip}`,
      'same day delivery',
      'lab tested cannabis',
      'licensed dispensary'
    ].join(', '),
    openGraph: {
      title,
      description,
      url: `https://weednearmedc.com/${state}/${city}/${neighborhood}`,
      siteName: 'WeedNearMeDC',
      locale: 'en_US',
      type: 'website',
      images: [
        {
          url: '/images/og-geo-delivery.jpg',
          width: 1200,
          height: 630,
          alt: `Cannabis delivery in ${areaName}, ${cityName}`
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: ['/images/og-geo-delivery.jpg']
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: `https://weednearmedc.com/${state}/${city}/${neighborhood}`
    }
  };
}

export default function NeighborhoodPage({ params }: PageProps) {
  const { state, city, neighborhood } = params;
  
  const stateData = DMV_AREAS[state as keyof typeof DMV_AREAS];
  const cityData = stateData?.cities[city];
  const neighborhoodData = cityData?.neighborhoods[neighborhood];
  
  if (!stateData || !cityData || !neighborhoodData) {
    notFound();
  }

  const areaName = neighborhoodData.name;
  const cityName = cityData.name;
  const stateName = stateData.name;
  const isRecreational = stateData.legalStatus === 'recreational';

  const jsonLdData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": `WeedNearMeDC - ${areaName}`,
    "description": `Premium cannabis delivery service in ${areaName}, ${cityName}`,
    "url": `https://weednearmedc.com/${state}/${city}/${neighborhood}`,
    "telephone": "+***********",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": cityName,
      "addressRegion": stateName,
      "postalCode": neighborhoodData.zip,
      "addressCountry": "US"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "38.9072",
      "longitude": "-77.0369"
    },
    "openingHours": "Mo-Su 10:00-22:00",
    "serviceArea": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": "38.9072",
        "longitude": "-77.0369"
      },
      "geoRadius": "50000"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "2847"
    }
  };

  return (
    <>
      <JsonLd data={jsonLdData} />
      
      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-[#005F4E] to-[#004A3E] text-white py-20">
          <div className="max-w-6xl mx-auto px-4">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <div className="flex items-center mb-4">
                  <MapPin className="w-6 h-6 text-[#C79500] mr-2" />
                  <span className="text-[#C79500] font-medium">
                    {isRecreational ? 'Recreational' : 'Medical'} Cannabis Delivery
                  </span>
                </div>
                
                <h1 className="text-5xl font-light tracking-tight mb-6">
                  Premium Cannabis
                  <span className="block text-[#C79500]">
                    Delivered to {areaName}
                  </span>
                </h1>
                
                <p className="text-xl text-white/90 mb-8">
                  Fast, discreet {isRecreational ? 'recreational' : 'medical'} cannabis delivery 
                  to {areaName}, {cityName}. Lab-tested products from licensed dispensaries.
                </p>

                <div className="grid grid-cols-2 gap-4 mb-8">
                  <div className="flex items-center">
                    <Clock className="w-5 h-5 text-[#C79500] mr-2" />
                    <span>{stateData.deliveryTime}</span>
                  </div>
                  <div className="flex items-center">
                    <Package className="w-5 h-5 text-[#C79500] mr-2" />
                    <span>Min order {stateData.minOrder}</span>
                  </div>
                  <div className="flex items-center">
                    <Shield className="w-5 h-5 text-[#C79500] mr-2" />
                    <span>Licensed & Compliant</span>
                  </div>
                  <div className="flex items-center">
                    <Star className="w-5 h-5 text-[#C79500] mr-2" />
                    <span>4.9/5 Rating</span>
                  </div>
                </div>

                <GeoAwareCTA 
                  area={areaName}
                  city={cityName}
                  state={stateName}
                  deliveryAvailable={neighborhoodData.deliveryAvailable}
                />
              </div>

              <div className="relative">
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8">
                  <h3 className="text-2xl font-medium mb-6">
                    Delivery Zone: {areaName}
                  </h3>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>ZIP Code:</span>
                      <span className="font-medium">{neighborhoodData.zip}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivery Time:</span>
                      <span className="font-medium">{stateData.deliveryTime}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Minimum Order:</span>
                      <span className="font-medium">{stateData.minOrder}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Legal Status:</span>
                      <span className="font-medium capitalize">{stateData.legalStatus}</span>
                    </div>
                  </div>

                  <button className="w-full mt-6 py-3 bg-[#C79500] text-white rounded-lg hover:bg-[#B08400] transition-colors">
                    Check Delivery Availability
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Local SEO Content */}
        <section className="py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-light mb-8">
              Cannabis Delivery in {areaName}, {cityName}
            </h2>
            
            <div className="prose max-w-none">
              <p className="text-lg text-gray-600 mb-6">
                WeedNearMeDC provides premium {isRecreational ? 'recreational' : 'medical'} cannabis 
                delivery to {areaName} and surrounding areas in {cityName}, {stateName}. 
                Our licensed dispensary partners offer lab-tested flower, edibles, vapes, 
                and concentrates with {stateData.deliveryTime} delivery.
              </p>

              <h3 className="text-2xl font-medium mb-4">
                {isRecreational ? 'Recreational' : 'Medical'} Cannabis Laws in {stateName}
              </h3>
              
              <p className="text-gray-600 mb-6">
                {isRecreational ? (
                  <>
                    {stateName} allows recreational cannabis for adults 21 and older. 
                    You can legally possess and consume cannabis in private spaces, 
                    and our delivery service brings premium products directly to your door in {areaName}.
                  </>
                ) : (
                  <>
                    {stateName} has a comprehensive medical cannabis program. Patients with 
                    valid recommendations can access high-quality cannabis products through 
                    our secure delivery network serving {areaName} and {cityName}.
                  </>
                )}
              </p>

              <h3 className="text-2xl font-medium mb-4">
                Delivery Areas Near {areaName}
              </h3>
              
              <div className="grid md:grid-cols-2 gap-4 mb-8">
                {Object.entries(cityData.neighborhoods).map(([key, area]) => (
                  <div key={key} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium">{area.name}</div>
                      <div className="text-sm text-gray-500">ZIP: {area.zip}</div>
                    </div>
                    <div className="flex items-center text-green-600">
                      <Truck className="w-4 h-4 mr-1" />
                      <span className="text-sm">Available</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 px-4 bg-gray-50">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-light mb-8">
              Frequently Asked Questions - {areaName} Delivery
            </h2>
            
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg">
                <h3 className="font-medium mb-2">
                  Do you deliver to {areaName}, {cityName}?
                </h3>
                <p className="text-gray-600">
                  Yes! We provide {stateData.deliveryTime} cannabis delivery to {areaName} 
                  and all surrounding neighborhoods in {cityName}. 
                  Our delivery zone covers ZIP code {neighborhoodData.zip} and nearby areas.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg">
                <h3 className="font-medium mb-2">
                  What's the minimum order for {areaName} delivery?
                </h3>
                <p className="text-gray-600">
                  The minimum order for delivery to {areaName} is {stateData.minOrder}. 
                  This helps us maintain sustainable delivery operations while keeping costs low.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg">
                <h3 className="font-medium mb-2">
                  What are the cannabis laws in {stateName}?
                </h3>
                <p className="text-gray-600">
                  {isRecreational ? (
                    `${stateName} allows recreational cannabis for adults 21+. You can legally 
                    possess and consume cannabis in private spaces throughout ${cityName}.`
                  ) : (
                    `${stateName} has a medical cannabis program requiring valid patient 
                    recommendations. Our delivery service serves qualified patients in ${areaName}.`
                  )}
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}