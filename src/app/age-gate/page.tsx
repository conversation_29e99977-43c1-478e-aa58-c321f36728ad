"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar, Shield, AlertTriangle } from "lucide-react";
import { APP_CONFIG } from "@/constants";

export default function AgeGatePage() {
  const [birthDate, setBirthDate] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const handleVerification = async () => {
    setError("");
    setIsVerifying(true);

    if (!birthDate) {
      setError("Please enter your date of birth");
      setIsVerifying(false);
      return;
    }

    const age = calculateAge(birthDate);
    
    if (age < APP_CONFIG.minAge) {
      setError(`You must be ${APP_CONFIG.minAge} or older to access this site`);
      setIsVerifying(false);
      return;
    }

    // Set age verification cookie
    document.cookie = `age_verified=true; path=/; max-age=${60 * 60 * 24 * 30}`; // 30 days
    
    // Redirect to Maryland shop (primary market)
    router.push("/md");
  };

  const handleExit = () => {
    window.location.href = "https://www.samhsa.gov/find-help/national-helpline";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center">
            <Shield className="h-8 w-8 text-amber-600" />
          </div>
          <CardTitle className="text-2xl font-bold">Age Verification Required</CardTitle>
          <CardDescription>
            You must be {APP_CONFIG.minAge} or older to access this cannabis delivery service
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Warning Notice */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <p className="font-medium mb-1">Legal Notice</p>
                <p>
                  Cannabis products are for adults {APP_CONFIG.minAge}+ only. 
                  Keep out of reach of children and pets. Do not operate vehicles or machinery.
                </p>
              </div>
            </div>
          </div>

          {/* Date Input */}
          <div className="space-y-2">
            <Label htmlFor="birthdate" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Date of Birth
            </Label>
            <Input
              id="birthdate"
              type="date"
              value={birthDate}
              onChange={(e) => setBirthDate(e.target.value)}
              max={new Date().toISOString().split('T')[0]}
              className="text-center"
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-800 text-sm font-medium">{error}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              onClick={handleExit}
              className="w-full"
            >
              I'm Under {APP_CONFIG.minAge}
            </Button>
            <Button
              onClick={handleVerification}
              disabled={isVerifying}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {isVerifying ? "Verifying..." : `I'm ${APP_CONFIG.minAge}+`}
            </Button>
          </div>

          {/* Legal Disclaimer */}
          <div className="text-xs text-gray-500 text-center space-y-1">
            <p>
              By entering this site, you acknowledge that you are of legal age 
              and agree to our Terms of Service and Privacy Policy.
            </p>
            <p>
              Cannabis has not been analyzed or approved by the FDA. 
              Please consume responsibly.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
