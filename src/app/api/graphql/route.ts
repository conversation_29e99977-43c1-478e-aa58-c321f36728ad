import { ApolloServer } from '@apollo/server'
import { startServerAndCreateNextHandler } from '@as-integrations/next'
import { typeDefs } from '@/lib/graphql/schema'
import { resolvers } from '@/lib/graphql/resolvers'
import { NextRequest } from 'next/server'

const server = new ApolloServer({
  typeDefs,
  resolvers,
  introspection: process.env.NODE_ENV !== 'production',
})

const handler = startServerAndCreateNextHandler<NextRequest>(server, {
  context: async (req) => {
    // For now, we'll use a simple context without Supabase auth
    // This will be updated when we implement proper authentication
    return {
      req,
      user: null, // No user authentication for now
    }
  },
})

export { handler as GET, handler as POST }
