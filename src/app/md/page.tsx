import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Search, 
  Filter, 
  ShoppingCart, 
  Star, 
  Truck, 
  Clock,
  Leaf,
  Zap,
  Heart
} from "lucide-react";
import Link from "next/link";
import { PRODUCT_CATEGORIES, STATES } from "@/constants";

// Mock product data for demonstration
const mockProducts = [
  {
    id: 1,
    name: "Blue Dream",
    category: "FLOWER",
    price: 4500, // $45.00
    compareAtPrice: 5000,
    weight: "3.5g",
    thcContent: "18-22%",
    strain: "Hybrid",
    rating: 4.8,
    reviewCount: 124,
    images: ["/images/products/blue-dream.jpg"],
    inStock: true,
    featured: true,
    effects: ["Creative", "Euphoric", "Relaxing"],
    flavors: ["<PERSON>", "Sweet", "Earthy"]
  },
  {
    id: 2,
    name: "Sour Diesel Gummies",
    category: "EDIBLES",
    price: 3000, // $30.00
    weight: "10mg x 10 pieces",
    thcContent: "10mg per piece",
    rating: 4.6,
    reviewCount: 89,
    images: ["/images/products/sour-gummies.jpg"],
    inStock: true,
    effects: ["Energizing", "Uplifting", "Focused"],
    flavors: ["Sour", "Citrus"]
  },
  {
    id: 3,
    name: "OG Kush Vape Cart",
    category: "VAPES",
    price: 5500, // $55.00
    weight: "1g",
    thcContent: "85-90%",
    strain: "Indica",
    rating: 4.9,
    reviewCount: 156,
    images: ["/images/products/og-kush-cart.jpg"],
    inStock: true,
    featured: true,
    effects: ["Relaxing", "Sleepy", "Happy"],
    flavors: ["Pine", "Earthy", "Diesel"]
  }
];

export default function MarylandShopPage() {
  const state = STATES.MD;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/" className="text-2xl font-bold text-green-600">
                WeedNearMeDC
              </Link>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {state.name}
              </Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="hidden md:flex items-center gap-2 text-sm text-gray-600">
                <Truck className="h-4 w-4" />
                <span>Free delivery on orders $75+</span>
              </div>
              <Button variant="outline" size="sm">
                <ShoppingCart className="h-4 w-4 mr-2" />
                Cart (0)
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Banner */}
      <section className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Premium Cannabis Delivery in Maryland
            </h1>
            <p className="text-xl mb-6 text-green-100">
              Shop our curated selection of top-shelf flower, edibles, vapes, and concentrates. 
              Same-day delivery available throughout Maryland.
            </p>
            <div className="flex flex-wrap gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>Delivery in 1-2 hours</span>
              </div>
              <div className="flex items-center gap-2">
                <Leaf className="h-4 w-4" />
                <span>Lab-tested products</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <span>Licensed dispensary</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search products..."
              className="pl-10"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </Button>
        </div>
      </section>

      {/* Product Categories */}
      <section className="container mx-auto px-4 py-6">
        <h2 className="text-2xl font-bold mb-6">Shop by Category</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          {Object.entries(PRODUCT_CATEGORIES).map(([key, category]) => (
            <Link
              key={key}
              href={`/md/products/${category.slug}`}
              className="group"
            >
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl mb-2">{category.icon}</div>
                  <h3 className="font-medium text-sm group-hover:text-green-600 transition-colors">
                    {category.label}
                  </h3>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </section>

      {/* Featured Products */}
      <section className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">Featured Products</h2>
          <Link href="/md/products" className="text-green-600 hover:text-green-700 font-medium">
            View All →
          </Link>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockProducts.map((product) => (
            <Card key={product.id} className="group hover:shadow-lg transition-shadow">
              <div className="relative">
                <div className="aspect-square bg-gray-200 rounded-t-lg flex items-center justify-center">
                  <Leaf className="h-16 w-16 text-gray-400" />
                </div>
                {product.featured && (
                  <Badge className="absolute top-2 left-2 bg-green-600">
                    Featured
                  </Badge>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  className="absolute top-2 right-2 h-8 w-8 p-0"
                >
                  <Heart className="h-4 w-4" />
                </Button>
              </div>
              
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg group-hover:text-green-600 transition-colors">
                      {product.name}
                    </CardTitle>
                    <CardDescription>
                      {PRODUCT_CATEGORIES[product.category as keyof typeof PRODUCT_CATEGORIES]?.label} • {product.weight}
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-lg">
                      ${(product.price / 100).toFixed(2)}
                    </div>
                    {product.compareAtPrice && (
                      <div className="text-sm text-gray-500 line-through">
                        ${(product.compareAtPrice / 100).toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center gap-2 mb-3">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">{product.rating}</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    ({product.reviewCount} reviews)
                  </span>
                </div>
                
                <div className="space-y-2 mb-4">
                  <div className="text-sm">
                    <span className="font-medium">THC:</span> {product.thcContent}
                  </div>
                  {product.strain && (
                    <div className="text-sm">
                      <span className="font-medium">Type:</span> {product.strain}
                    </div>
                  )}
                </div>
                
                <div className="flex flex-wrap gap-1 mb-4">
                  {product.effects.slice(0, 2).map((effect) => (
                    <Badge key={effect} variant="secondary" className="text-xs">
                      {effect}
                    </Badge>
                  ))}
                </div>
                
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
}
