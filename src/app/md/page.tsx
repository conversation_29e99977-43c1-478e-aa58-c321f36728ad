import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ProductGrid } from "@/components/cannabis/product-grid";
import {
  Search,
  Filter,
  ShoppingCart,
  Truck,
  Clock,
  Leaf,
  Zap
} from "lucide-react";
import Link from "next/link";
import { PRODUCT_CATEGORIES, STATES } from "@/constants";

export default function MarylandShopPage() {
  const state = STATES.MD;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/" className="text-2xl font-bold text-green-600">
                WeedNearMeDC
              </Link>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {state.name}
              </Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="hidden md:flex items-center gap-2 text-sm text-gray-600">
                <Truck className="h-4 w-4" />
                <span>Free delivery on orders $75+</span>
              </div>
              <Button variant="outline" size="sm">
                <ShoppingCart className="h-4 w-4 mr-2" />
                Cart (0)
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Banner */}
      <section className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Premium Cannabis Delivery in Maryland
            </h1>
            <p className="text-xl mb-6 text-green-100">
              Shop our curated selection of top-shelf flower, edibles, vapes, and concentrates. 
              Same-day delivery available throughout Maryland.
            </p>
            <div className="flex flex-wrap gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>Delivery in 1-2 hours</span>
              </div>
              <div className="flex items-center gap-2">
                <Leaf className="h-4 w-4" />
                <span>Lab-tested products</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <span>Licensed dispensary</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search products..."
              className="pl-10"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </Button>
        </div>
      </section>

      {/* Product Categories */}
      <section className="container mx-auto px-4 py-6">
        <h2 className="text-2xl font-bold mb-6">Shop by Category</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          {Object.entries(PRODUCT_CATEGORIES).map(([key, category]) => (
            <Link
              key={key}
              href={`/md/products/${category.slug}`}
              className="group"
            >
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl mb-2">{category.icon}</div>
                  <h3 className="font-medium text-sm group-hover:text-green-600 transition-colors">
                    {category.label}
                  </h3>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </section>

      {/* Featured Products */}
      <section className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">Featured Products</h2>
          <Link href="/md/products" className="text-green-600 hover:text-green-700 font-medium">
            View All →
          </Link>
        </div>

        <ProductGrid state="MD" featured={true} limit={6} />
      </section>

      {/* All Products */}
      <section className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">All Products</h2>
        </div>

        <ProductGrid state="MD" limit={12} />
      </section>
    </div>
  );
}
