import { MainLayout } from "@/components/layout/main-layout"
import { SchemaMarkup } from "@/components/seo/schema-markup"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { 
  Star, 
  ShoppingCart, 
  Heart, 
  ArrowLeft, 
  Leaf, 
  Check, 
  Info, 
  AlertTriangle,
  Truck,
  Clock
} from "lucide-react"
import Link from "next/link"
import { PRODUCT_CATEGORIES } from "@/constants"
import { Metadata } from "next"

// Mock product data - in production, this would be fetched from your database
const mockProducts = {
  "blue-dream": {
    id: 1,
    name: "Blue Dream",
    slug: "blue-dream",
    description: "A balanced hybrid strain that delivers swift symptom relief without heavy sedative effects. Blue Dream has a sweet berry aroma redolent of its Blueberry parent.",
    longDescription: "Blue Dream is a sativa-dominant hybrid marijuana strain made by crossing Blueberry with Haze. This strain produces a balanced high, along with effects such as cerebral stimulation and full-body relaxation. Blue dream is 18% THC but has a low CBD percentage, making this potent strain a fan favorite of both novice and veteran cannabis consumers. In terms of flavor, Blue Dream is reported to smell and taste like sweet berries. Medical marijuana patients often use Blue Dream to treat symptoms of depression, chronic pain, and nausea. According to home growers, this strain has an average flowering time of 67 days and is best suited to grow using the Sea of Green method. Blue Dream originated in California and has since achieved legendary status among West Coast strains and has quickly become one of the most-searched-for strains in the Leafly database.",
    category: "FLOWER",
    subcategory: "Hybrid",
    brand: "West Coast Cure",
    thcContent: "18-22%",
    cbdContent: "0.1-0.3%",
    strain: "Hybrid",
    effects: ["Creative", "Euphoric", "Relaxing", "Uplifting"],
    flavors: ["Berry", "Sweet", "Earthy"],
    price: 4500, // $45.00
    compareAtPrice: 5000,
    weight: "3.5g",
    inStock: true,
    stockCount: 25,
    featured: true,
    onSale: true,
    images: ["/images/products/blue-dream.jpg"],
    rating: 4.8,
    reviewCount: 124,
    labTested: true,
    availableInMD: true,
    availableInDC: false,
    availableInVA: false,
    reviews: [
      {
        id: 1,
        userId: "user1",
        userName: "John D.",
        rating: 5,
        title: "Best hybrid strain",
        body: "Perfect balance of relaxation and energy. Great for daytime use without feeling too sedated.",
        verified: true,
        createdAt: new Date("2024-01-15")
      },
      {
        id: 2,
        userId: "user2",
        userName: "Sarah M.",
        rating: 4,
        title: "Good for anxiety",
        body: "Really helps with my anxiety without making me too sleepy. Nice berry flavor too.",
        verified: true,
        createdAt: new Date("2024-01-10")
      },
      {
        id: 3,
        userId: "user3",
        userName: "Mike T.",
        rating: 5,
        title: "Consistent quality",
        body: "Always reliable quality. One of my go-to strains for creative work.",
        verified: true,
        createdAt: new Date("2024-01-05")
      }
    ]
  }
}

interface ProductPageProps {
  params: {
    category: string
    slug: string
  }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const { category, slug } = params
  
  // In production, fetch the product data from your database
  const product = mockProducts[slug as keyof typeof mockProducts]
  
  if (!product) {
    return {
      title: "Product Not Found",
      description: "The requested product could not be found."
    }
  }

  const categoryInfo = Object.values(PRODUCT_CATEGORIES).find(c => c.slug === category)
  
  return {
    title: `${product.name} ${categoryInfo?.label || ''} | ${product.strain || ''} Cannabis | WeedNearMeDC`,
    description: `${product.description} Premium ${product.strain || ''} ${categoryInfo?.label || ''} with ${product.thcContent} THC. Fast delivery in Maryland.`,
    keywords: `${product.name}, ${product.strain || ''} cannabis, ${categoryInfo?.label || ''}, cannabis delivery maryland, ${product.effects.join(', ')}, ${product.flavors.join(', ')}`,
    openGraph: {
      title: `${product.name} - Premium ${product.strain || ''} ${categoryInfo?.label || ''}`,
      description: product.description,
      url: `https://weednearmedc.com/md/products/${category}/${slug}`,
      images: [
        {
          url: product.images[0] || "/images/product-placeholder.jpg",
          width: 1200,
          height: 630,
          alt: `${product.name} - ${product.strain || ''} Cannabis`
        }
      ],
      type: "product"
    }
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  const { category, slug } = params
  
  // In production, fetch the product data from your database
  const product = mockProducts[slug as keyof typeof mockProducts]
  
  if (!product) {
    return (
      <MainLayout>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Product Not Found</h1>
              <p className="text-gray-600 mb-8">
                The requested product could not be found.
              </p>
              <Button asChild className="bg-green-600 hover:bg-green-700">
                <Link href="/md">
                  Browse Products
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }

  const categoryInfo = Object.values(PRODUCT_CATEGORIES).find(c => c.slug === category)
  
  return (
    <MainLayout>
      <SchemaMarkup type="product" data={product} />
      <SchemaMarkup 
        type="breadcrumb" 
        data={{
          items: [
            { name: "Home", url: "/" },
            { name: "Maryland", url: "/md" },
            { name: categoryInfo?.label || "Products", url: `/md/products/${category}` },
            { name: product.name, url: `/md/products/${category}/${slug}` }
          ]
        }}
      />
      
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Breadcrumb */}
          <div className="mb-6">
            <Button variant="ghost" asChild className="mb-4 p-0 h-auto hover:bg-transparent">
              <Link href="/md" className="flex items-center text-sm text-gray-600 hover:text-gray-900">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Products
              </Link>
            </Button>
            
            <nav className="flex text-sm text-gray-500">
              <Link href="/" className="hover:text-gray-900">Home</Link>
              <span className="mx-2">/</span>
              <Link href="/md" className="hover:text-gray-900">Maryland</Link>
              <span className="mx-2">/</span>
              <Link href={`/md/products/${category}`} className="hover:text-gray-900">
                {categoryInfo?.label || "Products"}
              </Link>
              <span className="mx-2">/</span>
              <span className="text-gray-900 font-medium">{product.name}</span>
            </nav>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Product Image */}
            <div>
              <div className="bg-white rounded-lg overflow-hidden border">
                <div className="aspect-square bg-gray-100 flex items-center justify-center">
                  <Leaf className="h-32 w-32 text-gray-300" />
                </div>
              </div>
            </div>

            {/* Product Info */}
            <div>
              <div className="mb-4">
                {product.brand && (
                  <div className="text-sm text-gray-600 mb-1">{product.brand}</div>
                )}
                <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
                <div className="flex items-center gap-2 mt-2">
                  <div className="flex items-center">
                    <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
                    <span className="ml-1 font-medium">{product.rating}</span>
                  </div>
                  <span className="text-gray-600">
                    ({product.reviewCount} reviews)
                  </span>
                  <Badge variant="outline" className="ml-2">
                    {product.strain}
                  </Badge>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-gray-700">{product.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600">THC Content</div>
                  <div className="font-semibold">{product.thcContent}</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600">CBD Content</div>
                  <div className="font-semibold">{product.cbdContent}</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600">Weight</div>
                  <div className="font-semibold">{product.weight}</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600">Type</div>
                  <div className="font-semibold">{product.strain}</div>
                </div>
              </div>

              <div className="mb-6">
                <div className="text-sm text-gray-600 mb-2">Effects</div>
                <div className="flex flex-wrap gap-2">
                  {product.effects.map(effect => (
                    <Badge key={effect} variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                      {effect}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <div className="text-sm text-gray-600 mb-2">Flavors</div>
                <div className="flex flex-wrap gap-2">
                  {product.flavors.map(flavor => (
                    <Badge key={flavor} variant="secondary" className="bg-green-50 text-green-700 border-green-200">
                      {flavor}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="text-3xl font-bold text-gray-900">
                    ${(product.price / 100).toFixed(2)}
                  </div>
                  {product.compareAtPrice && (
                    <div className="text-gray-500 line-through">
                      ${(product.compareAtPrice / 100).toFixed(2)}
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="flex items-center gap-1 text-green-600">
                    <Check className="h-4 w-4" />
                    <span>In Stock</span>
                  </div>
                  {product.labTested && (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      Lab Tested
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex gap-4 mb-6">
                <Button className="flex-1 bg-green-600 hover:bg-green-700">
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  Add to Cart
                </Button>
                <Button variant="outline" className="w-12 p-0">
                  <Heart className="h-5 w-5" />
                </Button>
              </div>

              <div className="flex flex-col gap-3 text-sm">
                <div className="flex items-center gap-2 text-gray-600">
                  <Truck className="h-4 w-4 text-green-600" />
                  <span>Free delivery on orders over $75</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Clock className="h-4 w-4 text-green-600" />
                  <span>Same-day delivery available</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <AlertTriangle className="h-4 w-4 text-amber-600" />
                  <span>Must be 21+ with valid ID to purchase</span>
                </div>
              </div>
            </div>
          </div>

          {/* Product Details Tabs */}
          <div className="mt-12">
            <Tabs defaultValue="description">
              <TabsList className="w-full justify-start">
                <TabsTrigger value="description">Description</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="reviews">Reviews ({product.reviewCount})</TabsTrigger>
              </TabsList>
              <TabsContent value="description" className="mt-6">
                <Card>
                  <CardContent className="pt-6">
                    <p className="text-gray-700 whitespace-pre-line">
                      {product.longDescription || product.description}
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="details" className="mt-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-semibold mb-4">Product Specifications</h3>
                        <div className="space-y-3">
                          <div className="flex justify-between py-2 border-b">
                            <span className="text-gray-600">Brand</span>
                            <span className="font-medium">{product.brand}</span>
                          </div>
                          <div className="flex justify-between py-2 border-b">
                            <span className="text-gray-600">Strain</span>
                            <span className="font-medium">{product.strain}</span>
                          </div>
                          <div className="flex justify-between py-2 border-b">
                            <span className="text-gray-600">THC Content</span>
                            <span className="font-medium">{product.thcContent}</span>
                          </div>
                          <div className="flex justify-between py-2 border-b">
                            <span className="text-gray-600">CBD Content</span>
                            <span className="font-medium">{product.cbdContent}</span>
                          </div>
                          <div className="flex justify-between py-2 border-b">
                            <span className="text-gray-600">Weight</span>
                            <span className="font-medium">{product.weight}</span>
                          </div>
                          <div className="flex justify-between py-2 border-b">
                            <span className="text-gray-600">Lab Tested</span>
                            <span className="font-medium">{product.labTested ? 'Yes' : 'No'}</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold mb-4">Usage Information</h3>
                        <div className="space-y-4">
                          <div>
                            <div className="font-medium mb-1">Effects</div>
                            <div className="flex flex-wrap gap-2">
                              {product.effects.map(effect => (
                                <Badge key={effect} variant="secondary">
                                  {effect}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div>
                            <div className="font-medium mb-1">Flavors</div>
                            <div className="flex flex-wrap gap-2">
                              {product.flavors.map(flavor => (
                                <Badge key={flavor} variant="secondary">
                                  {flavor}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="pt-4">
                            <div className="flex items-start gap-2 p-4 bg-amber-50 rounded-lg">
                              <Info className="h-5 w-5 text-amber-600 mt-0.5" />
                              <div>
                                <div className="font-medium text-amber-800">Consumption Advice</div>
                                <p className="text-sm text-amber-700 mt-1">
                                  Start with a small amount and wait at least 15-30 minutes before consuming more. Effects may vary by individual. Do not drive or operate heavy machinery while using this product.
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="reviews" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Customer Reviews</CardTitle>
                    <CardDescription>
                      {product.reviewCount} reviews with an average rating of {product.rating} out of 5 stars
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {product.reviews?.map(review => (
                        <div key={review.id} className="pb-6 border-b last:border-0">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <div className="flex items-center gap-2">
                                <div className="flex">
                                  {Array.from({ length: 5 }).map((_, i) => (
                                    <Star 
                                      key={i} 
                                      className={`h-4 w-4 ${i < review.rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
                                    />
                                  ))}
                                </div>
                                <h4 className="font-medium">{review.title}</h4>
                              </div>
                              <div className="text-sm text-gray-600 mt-1">
                                {review.userName}
                                {review.verified && (
                                  <Badge variant="outline" className="ml-2 text-xs bg-green-50 text-green-700 border-green-200">
                                    Verified Buyer
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className="text-sm text-gray-500">
                              {review.createdAt.toLocaleDateString()}
                            </div>
                          </div>
                          <p className="text-gray-700">{review.body}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
