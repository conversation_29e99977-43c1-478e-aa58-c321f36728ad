import { initTRPC, TRPCError } from '@trpc/server'
import { PrismaClient } from '@prisma/client'
import { z } from 'zod'

const prisma = new PrismaClient()

// Create context
export const createTRPCContext = async () => {
  // For now, we'll use a simple context without Supabase auth
  return {
    user: null, // No user authentication for now
    prisma
  }
}

type Context = Awaited<ReturnType<typeof createTRPCContext>>

// Initialize tRPC
const t = initTRPC.context<Context>().create()

// Middleware for authentication
const isAuthed = t.middleware(({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' })
  }
  return next({
    ctx: {
      ...ctx,
      user: ctx.user
    }
  })
})

// Base router and procedures
export const router = t.router
export const publicProcedure = t.procedure
export const protectedProcedure = t.procedure.use(isAuthed)

// Product procedures
const productRouter = router({
  getAll: publicProcedure
    .input(z.object({
      category: z.string().optional(),
      state: z.enum(['DC', 'MD', 'VA']).optional(),
      featured: z.boolean().optional(),
      limit: z.number().min(1).max(100).default(20),
      cursor: z.number().optional()
    }))
    .query(async ({ input, ctx }) => {
      const { category, state, featured, limit, cursor } = input
      
      const where: any = {}
      if (category) where.category = category
      if (featured !== undefined) where.featured = featured
      if (state === 'DC') where.availableInDC = true
      if (state === 'MD') where.availableInMD = true
      if (state === 'VA') where.availableInVA = true

      const products = await ctx.prisma.product.findMany({
        where,
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { createdAt: 'desc' },
        include: {
          reviews: {
            take: 3,
            orderBy: { createdAt: 'desc' }
          }
        }
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (products.length > limit) {
        const nextItem = products.pop()
        nextCursor = nextItem!.id
      }

      return {
        products,
        nextCursor
      }
    }),

  getBySlug: publicProcedure
    .input(z.object({
      slug: z.string()
    }))
    .query(async ({ input, ctx }) => {
      const product = await ctx.prisma.product.findUnique({
        where: { slug: input.slug },
        include: {
          reviews: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true
                }
              }
            },
            orderBy: { createdAt: 'desc' }
          }
        }
      })

      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found'
        })
      }

      return product
    }),

  search: publicProcedure
    .input(z.object({
      query: z.string(),
      category: z.string().optional(),
      state: z.enum(['DC', 'MD', 'VA']).optional(),
      limit: z.number().min(1).max(50).default(20)
    }))
    .query(async ({ input, ctx }) => {
      const { query, category, state, limit } = input
      
      const where: any = {
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { effects: { hasSome: [query] } },
          { flavors: { hasSome: [query] } }
        ]
      }

      if (category) where.category = category
      if (state === 'DC') where.availableInDC = true
      if (state === 'MD') where.availableInMD = true
      if (state === 'VA') where.availableInVA = true

      return await ctx.prisma.product.findMany({
        where,
        take: limit,
        orderBy: { rating: 'desc' }
      })
    })
})

// Cart procedures
const cartRouter = router({
  get: protectedProcedure
    .query(async ({ ctx }) => {
      return await ctx.prisma.cartItem.findMany({
        where: { userId: ctx.user.id },
        include: {
          product: true
        },
        orderBy: { createdAt: 'desc' }
      })
    }),

  add: protectedProcedure
    .input(z.object({
      productId: z.number(),
      quantity: z.number().min(1)
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, quantity } = input

      // Check if product exists and is in stock
      const product = await ctx.prisma.product.findUnique({
        where: { id: productId }
      })

      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found'
        })
      }

      if (!product.inStock) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Product is out of stock'
        })
      }

      // Check if item already exists in cart
      const existingItem = await ctx.prisma.cartItem.findUnique({
        where: {
          userId_productId: {
            userId: ctx.user.id,
            productId
          }
        }
      })

      if (existingItem) {
        // Update quantity
        return await ctx.prisma.cartItem.update({
          where: { id: existingItem.id },
          data: { quantity: existingItem.quantity + quantity },
          include: { product: true }
        })
      } else {
        // Create new cart item
        return await ctx.prisma.cartItem.create({
          data: {
            userId: ctx.user.id,
            productId,
            quantity
          },
          include: { product: true }
        })
      }
    }),

  update: protectedProcedure
    .input(z.object({
      id: z.number(),
      quantity: z.number().min(0)
    }))
    .mutation(async ({ input, ctx }) => {
      const { id, quantity } = input

      const cartItem = await ctx.prisma.cartItem.findUnique({
        where: { id }
      })

      if (!cartItem || cartItem.userId !== ctx.user.id) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Cart item not found'
        })
      }

      if (quantity === 0) {
        await ctx.prisma.cartItem.delete({ where: { id } })
        return { deleted: true }
      }

      return await ctx.prisma.cartItem.update({
        where: { id },
        data: { quantity },
        include: { product: true }
      })
    }),

  remove: protectedProcedure
    .input(z.object({
      id: z.number()
    }))
    .mutation(async ({ input, ctx }) => {
      const cartItem = await ctx.prisma.cartItem.findUnique({
        where: { id: input.id }
      })

      if (!cartItem || cartItem.userId !== ctx.user.id) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Cart item not found'
        })
      }

      await ctx.prisma.cartItem.delete({ where: { id: input.id } })
      return { success: true }
    }),

  clear: protectedProcedure
    .mutation(async ({ ctx }) => {
      await ctx.prisma.cartItem.deleteMany({
        where: { userId: ctx.user.id }
      })
      return { success: true }
    })
})

// Main app router
export const appRouter = router({
  products: productRouter,
  cart: cartRouter
})

export type AppRouter = typeof appRouter
