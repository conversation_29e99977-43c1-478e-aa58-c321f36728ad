import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Real-time subscriptions for live updates
export const subscribeToTable = (
  table: string,
  callback: (payload: any) => void,
  filter?: string
) => {
  return supabase
    .channel(`${table}-changes`)
    .on(
      'postgres_changes',
      { 
        event: '*', 
        schema: 'public', 
        table,
        filter 
      },
      callback
    )
    .subscribe();
};

// Optimized product search with pgvector
export const searchProducts = async (
  query: string,
  location: string,
  limit: number = 20
) => {
  const { data, error } = await supabase.rpc('search_products_vector', {
    search_query: query,
    user_location: location,
    result_limit: limit
  });

  if (error) throw error;
  return data;
};

// Analytics and metrics
export const trackEvent = async (
  event_type: string,
  properties: Record<string, any>,
  user_id?: string
) => {
  const { error } = await supabase.from('analytics_events').insert({
    event_type,
    properties,
    user_id,
    timestamp: new Date().toISOString()
  });

  if (error) console.error('Analytics tracking error:', error);
};

export default supabase;