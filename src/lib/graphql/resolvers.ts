import { PrismaClient } from '@prisma/client'
import { GraphQLError } from 'graphql'

const prisma = new PrismaClient()

export const resolvers = {
  Query: {
    // Products
    products: async (_: any, args: any, context: any) => {
      const { first = 20, after, filters, sort } = args
      
      const where: any = {}
      
      if (filters) {
        if (filters.category) where.category = filters.category
        if (filters.minPrice) where.price = { ...where.price, gte: filters.minPrice }
        if (filters.maxPrice) where.price = { ...where.price, lte: filters.maxPrice }
        if (filters.strain) where.strain = filters.strain
        if (filters.effects) where.effects = { hasSome: filters.effects }
        if (filters.inStock !== undefined) where.inStock = filters.inStock
        if (filters.onSale !== undefined) where.onSale = filters.onSale
        if (filters.featured !== undefined) where.featured = filters.featured
        
        // State availability
        if (filters.state === 'DC') where.availableInDC = true
        if (filters.state === 'MD') where.availableInMD = true
        if (filters.state === 'VA') where.availableInVA = true
      }

      const orderBy: any = {}
      if (sort) {
        switch (sort.field) {
          case 'NAME':
            orderBy.name = sort.direction.toLowerCase()
            break
          case 'PRICE':
            orderBy.price = sort.direction.toLowerCase()
            break
          case 'RATING':
            orderBy.rating = sort.direction.toLowerCase()
            break
          case 'CREATED_AT':
            orderBy.createdAt = sort.direction.toLowerCase()
            break
        }
      } else {
        orderBy.createdAt = 'desc'
      }

      const products = await prisma.product.findMany({
        where,
        orderBy,
        take: first + 1, // Take one extra to check if there's a next page
        skip: after ? 1 : 0,
        cursor: after ? { id: parseInt(after) } : undefined,
        include: {
          reviews: {
            take: 5,
            orderBy: { createdAt: 'desc' }
          }
        }
      })

      const hasNextPage = products.length > first
      const edges = products.slice(0, first).map(product => ({
        node: product,
        cursor: product.id.toString()
      }))

      const totalCount = await prisma.product.count({ where })

      return {
        edges,
        pageInfo: {
          hasNextPage,
          hasPreviousPage: !!after,
          startCursor: edges[0]?.cursor,
          endCursor: edges[edges.length - 1]?.cursor
        },
        totalCount
      }
    },

    product: async (_: any, args: any) => {
      const { id, slug } = args
      
      const where = id ? { id: parseInt(id) } : { slug }
      
      return await prisma.product.findUnique({
        where,
        include: {
          reviews: {
            include: {
              user: true
            },
            orderBy: { createdAt: 'desc' }
          }
        }
      })
    },

    // User & Auth
    me: async (_: any, __: any, context: any) => {
      // For now, return null since we don't have auth set up
      return null
    },

    // Orders
    myOrders: async (_: any, __: any, context: any) => {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) throw new GraphQLError('Authentication required')
      
      return await prisma.order.findMany({
        where: { userId: user.id },
        include: {
          items: {
            include: {
              product: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })
    },

    order: async (_: any, args: any, context: any) => {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) throw new GraphQLError('Authentication required')
      
      const order = await prisma.order.findUnique({
        where: { id: parseInt(args.id) },
        include: {
          items: {
            include: {
              product: true
            }
          }
        }
      })

      if (!order || order.userId !== user.id) {
        throw new GraphQLError('Order not found')
      }

      return order
    },

    // Cart
    myCart: async (_: any, __: any, context: any) => {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) throw new GraphQLError('Authentication required')
      
      return await prisma.cartItem.findMany({
        where: { userId: user.id },
        include: {
          product: true
        },
        orderBy: { createdAt: 'desc' }
      })
    },

    // Reviews
    productReviews: async (_: any, args: any) => {
      const { productId, first = 20, after } = args
      
      return await prisma.review.findMany({
        where: { productId: parseInt(productId) },
        include: {
          user: true
        },
        orderBy: { createdAt: 'desc' },
        take: first,
        skip: after ? 1 : 0,
        cursor: after ? { id: parseInt(after) } : undefined
      })
    }
  },

  Mutation: {
    // Cart
    addToCart: async (_: any, args: any, context: any) => {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) throw new GraphQLError('Authentication required')
      
      const { productId, quantity } = args
      
      // Check if item already exists in cart
      const existingItem = await prisma.cartItem.findUnique({
        where: {
          userId_productId: {
            userId: user.id,
            productId: parseInt(productId)
          }
        }
      })

      if (existingItem) {
        // Update quantity
        return await prisma.cartItem.update({
          where: { id: existingItem.id },
          data: { quantity: existingItem.quantity + quantity },
          include: { product: true }
        })
      } else {
        // Create new cart item
        return await prisma.cartItem.create({
          data: {
            userId: user.id,
            productId: parseInt(productId),
            quantity
          },
          include: { product: true }
        })
      }
    },

    updateCartItem: async (_: any, args: any, context: any) => {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) throw new GraphQLError('Authentication required')
      
      const { id, quantity } = args
      
      const cartItem = await prisma.cartItem.findUnique({
        where: { id: parseInt(id) }
      })

      if (!cartItem || cartItem.userId !== user.id) {
        throw new GraphQLError('Cart item not found')
      }

      if (quantity <= 0) {
        await prisma.cartItem.delete({ where: { id: parseInt(id) } })
        return null
      }

      return await prisma.cartItem.update({
        where: { id: parseInt(id) },
        data: { quantity },
        include: { product: true }
      })
    },

    removeFromCart: async (_: any, args: any, context: any) => {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) throw new GraphQLError('Authentication required')
      
      const cartItem = await prisma.cartItem.findUnique({
        where: { id: parseInt(args.id) }
      })

      if (!cartItem || cartItem.userId !== user.id) {
        throw new GraphQLError('Cart item not found')
      }

      await prisma.cartItem.delete({ where: { id: parseInt(args.id) } })
      return true
    },

    clearCart: async (_: any, __: any, context: any) => {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) throw new GraphQLError('Authentication required')
      
      await prisma.cartItem.deleteMany({
        where: { userId: user.id }
      })
      
      return true
    }
  }
}
