import { gql } from 'graphql-tag'

export const typeDefs = gql`
  scalar DateTime
  scalar JSON

  type User {
    id: ID!
    email: String!
    firstName: String
    lastName: String
    phone: String
    dateOfBirth: DateTime
    ageVerified: Boolean!
    idVerified: Boolean!
    state: String
    city: String
    zipCode: String
    address: String
    role: UserRole!
    createdAt: DateTime!
    updatedAt: DateTime!
    orders: [Order!]!
    reviews: [Review!]!
    loyalty: Loyalty
  }

  enum UserRole {
    CUSTOMER
    DRIVER
    ADMIN
    SUPER_ADMIN
  }

  type Product {
    id: ID!
    name: String!
    slug: String!
    description: String
    category: ProductCategory!
    subcategory: String
    thcContent: String
    cbdContent: String
    strain: String
    effects: [String!]!
    flavors: [String!]!
    price: Int!
    compareAtPrice: Int
    weight: String
    inStock: Boolean!
    stockCount: Int!
    featured: Boolean!
    onSale: Boolean!
    images: [String!]!
    videoUrl: String
    rating: Float!
    reviewCount: Int!
    labTested: Boolean!
    labResults: String
    availableInDC: Boolean!
    availableInMD: Boolean!
    availableInVA: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    reviews: [Review!]!
  }

  enum ProductCategory {
    FLOWER
    EDIBLES
    VAPES
    CONCENTRATES
    TOPICALS
    ACCESSORIES
    PRE_ROLLS
    TINCTURES
  }

  type CartItem {
    id: ID!
    userId: ID!
    productId: ID!
    quantity: Int!
    createdAt: DateTime!
    updatedAt: DateTime!
    user: User!
    product: Product!
  }

  type Order {
    id: ID!
    userId: ID!
    orderNumber: String!
    status: OrderStatus!
    total: Int!
    subtotal: Int!
    tax: Int!
    tip: Int!
    deliveryFee: Int!
    deliveryAddress: String!
    deliveryCity: String!
    deliveryState: String!
    deliveryZip: String!
    deliveryNotes: String
    driverId: ID
    estimatedDelivery: DateTime
    actualDelivery: DateTime
    paymentMethod: String
    paymentStatus: PaymentStatus!
    createdAt: DateTime!
    updatedAt: DateTime!
    user: User!
    items: [OrderItem!]!
  }

  enum OrderStatus {
    PENDING
    CONFIRMED
    PREPARING
    OUT_FOR_DELIVERY
    DELIVERED
    CANCELLED
    REFUNDED
  }

  enum PaymentStatus {
    PENDING
    PROCESSING
    COMPLETED
    FAILED
    REFUNDED
  }

  type OrderItem {
    id: ID!
    orderId: ID!
    productId: ID!
    quantity: Int!
    price: Int!
    order: Order!
    product: Product!
  }

  type Review {
    id: ID!
    userId: ID!
    productId: ID!
    rating: Int!
    title: String
    body: String
    verified: Boolean!
    helpful: Int!
    createdAt: DateTime!
    updatedAt: DateTime!
    user: User!
    product: Product!
  }

  type Loyalty {
    userId: ID!
    points: Int!
    orders: Int!
    tier: LoyaltyTier!
    badges: [String!]!
    strainsDiscovered: Int!
    categoriesExplored: Int!
    greenhouseLevel: Int!
    plantsOwned: [String!]!
    referralCode: String!
    referralsCount: Int!
    createdAt: DateTime!
    updatedAt: DateTime!
    user: User!
  }

  enum LoyaltyTier {
    BRONZE
    SILVER
    GOLD
    PLATINUM
    DIAMOND
  }

  input ProductFilters {
    category: ProductCategory
    minPrice: Int
    maxPrice: Int
    strain: String
    effects: [String!]
    inStock: Boolean
    onSale: Boolean
    featured: Boolean
    state: String
  }

  input ProductSort {
    field: ProductSortField!
    direction: SortDirection!
  }

  enum ProductSortField {
    NAME
    PRICE
    RATING
    CREATED_AT
  }

  enum SortDirection {
    ASC
    DESC
  }

  type ProductConnection {
    edges: [ProductEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type ProductEdge {
    node: Product!
    cursor: String!
  }

  type PageInfo {
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
    startCursor: String
    endCursor: String
  }

  type Query {
    # Products
    products(
      first: Int
      after: String
      filters: ProductFilters
      sort: ProductSort
    ): ProductConnection!
    
    product(id: ID, slug: String): Product
    
    # User & Auth
    me: User
    
    # Orders
    myOrders: [Order!]!
    order(id: ID!): Order
    
    # Cart
    myCart: [CartItem!]!
    
    # Reviews
    productReviews(productId: ID!, first: Int, after: String): [Review!]!
  }

  type Mutation {
    # Cart
    addToCart(productId: ID!, quantity: Int!): CartItem!
    updateCartItem(id: ID!, quantity: Int!): CartItem!
    removeFromCart(id: ID!): Boolean!
    clearCart: Boolean!
    
    # Orders
    createOrder(input: CreateOrderInput!): Order!
    updateOrderStatus(id: ID!, status: OrderStatus!): Order!
    
    # Reviews
    createReview(input: CreateReviewInput!): Review!
    updateReview(id: ID!, input: UpdateReviewInput!): Review!
    deleteReview(id: ID!): Boolean!
    
    # User
    updateProfile(input: UpdateProfileInput!): User!
  }

  input CreateOrderInput {
    deliveryAddress: String!
    deliveryCity: String!
    deliveryState: String!
    deliveryZip: String!
    deliveryNotes: String
    paymentMethod: String!
    tip: Int
  }

  input CreateReviewInput {
    productId: ID!
    rating: Int!
    title: String
    body: String
  }

  input UpdateReviewInput {
    rating: Int
    title: String
    body: String
  }

  input UpdateProfileInput {
    firstName: String
    lastName: String
    phone: String
    address: String
    city: String
    zipCode: String
  }

  type Subscription {
    orderStatusUpdated(orderId: ID!): Order!
    deliveryLocationUpdated(orderId: ID!): JSON!
  }
`
