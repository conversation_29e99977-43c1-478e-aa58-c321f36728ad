"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@/types'

interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  logout: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
}

interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone: string
  dateOfBirth: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for existing session on mount
    checkSession()
  }, [])

  const checkSession = async () => {
    try {
      // TODO: Implement Supabase session check
      // For now, check localStorage for demo purposes
      const savedUser = localStorage.getItem('demo_user')
      if (savedUser) {
        setUser(JSON.parse(savedUser))
      }
    } catch (error) {
      console.error('Error checking session:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      // TODO: Implement Supabase authentication
      // For now, create a demo user
      const demoUser: User = {
        id: 'demo-user-id',
        email,
        firstName: 'Demo',
        lastName: 'User',
        ageVerified: true,
        idVerified: false,
        role: 'CUSTOMER',
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setUser(demoUser)
      localStorage.setItem('demo_user', JSON.stringify(demoUser))
    } catch (error) {
      throw new Error('Invalid credentials')
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: RegisterData) => {
    setIsLoading(true)
    try {
      // TODO: Implement Supabase user registration
      const newUser: User = {
        id: 'new-user-id',
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
        dateOfBirth: new Date(userData.dateOfBirth),
        ageVerified: true,
        idVerified: false,
        role: 'CUSTOMER',
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      setUser(newUser)
      localStorage.setItem('demo_user', JSON.stringify(newUser))
    } catch (error) {
      throw new Error('Registration failed')
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      // TODO: Implement Supabase logout
      setUser(null)
      localStorage.removeItem('demo_user')
    } catch (error) {
      console.error('Error logging out:', error)
    }
  }

  const updateProfile = async (data: Partial<User>) => {
    if (!user) return
    
    try {
      // TODO: Implement Supabase profile update
      const updatedUser = { ...user, ...data, updatedAt: new Date() }
      setUser(updatedUser)
      localStorage.setItem('demo_user', JSON.stringify(updatedUser))
    } catch (error) {
      throw new Error('Failed to update profile')
    }
  }

  const value = {
    user,
    isLoading,
    login,
    register,
    logout,
    updateProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
