'use client';

import React, { useState, useEffect } from 'react';
import { MapPin, Clock, Shield, ChevronRight, Menu, X, Phone, Search, Star, Package, Truck, Filter, Info, CheckCircle, CreditCard, MessageSquare, Gift, TrendingUp, AlertCircle, ChevronDown, Zap, DollarSign, ArrowRight, ShoppingBag, Heart, Camera, Trophy } from 'lucide-react';
import { products, blogPosts, faqs, reviews } from '@/data/products';
import ScrollReactiveHero from '@/components/hero/ScrollReactiveHero';
import CannaQuest from '@/components/ar/CannaQuest';
import { trackEvent } from '@/lib/supabase/client';

const WeedNearMeDC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('DC');
  const [isAgeVerified, setIsAgeVerified] = useState(false);
  const [activeView, setActiveView] = useState('home');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [cart, setCart] = useState([]);
  const [showCheckout, setShowCheckout] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [showExitIntent, setShowExitIntent] = useState(false);
  const [showMedicalWizard, setShowMedicalWizard] = useState(false);
  const [medicalStep, setMedicalStep] = useState(1);
  const [showNotification, setShowNotification] = useState(false);
  const [selectedBlogPost, setSelectedBlogPost] = useState(null);
  const [expandedFaq, setExpandedFaq] = useState(null);
  const [currentProductPage, setCurrentProductPage] = useState(null);
  const [sortBy, setSortBy] = useState('featured');
  const [priceRange, setPriceRange] = useState([0, 200]);
  const [selectedEffects, setSelectedEffects] = useState([]);
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showCannaQuest, setShowCannaQuest] = useState(false);
  const [userOrderCount, setUserOrderCount] = useState(0);

  // A/B Testing variants
  const [ctaVariant, setCtaVariant] = useState('A');
  const ctaTexts = {
    A: 'Check Delivery Availability',
    B: 'Get Cannabis Delivered Now',
    C: 'Order for Same-Day Delivery'
  };

  // Location data
  const locations = {
    DC: {
      title: "Washington, DC",
      subtitle: "Medical Cannabis Available",
      legal: "Medical card required - Self-certification available",
      delivery: "Same-day delivery across DC",
      deliveryTime: "2-4 hours",
      minOrder: "$100",
      neighborhoods: ["U Street", "Adams Morgan", "Georgetown", "Capitol Hill", "Dupont Circle"]
    },
    MD: {
      title: "Maryland",
      subtitle: "Recreational & Medical",
      legal: "21+ for recreational, medical cards accepted",
      delivery: "Delivery available in Baltimore, Silver Spring, Rockville",
      deliveryTime: "3-5 hours",
      minOrder: "$75",
      neighborhoods: ["Baltimore", "Silver Spring", "Rockville", "Bethesda", "College Park"]
    },
    VA: {
      title: "Virginia",
      subtitle: "Medical Only",
      legal: "Valid medical recommendation required",
      delivery: "Delivery to registered patients",
      deliveryTime: "Next day",
      minOrder: "$150",
      neighborhoods: ["Richmond", "Alexandria", "Arlington", "Norfolk", "Virginia Beach"]
    }
  };

  // Derived data
  const brands = [...new Set(products.map(p => p.brand))].sort();
  const allEffects = [...new Set(products.flatMap(p => p.effects || []))].sort();

  // Track user behavior for exit intent
  useEffect(() => {
    const handleMouseLeave = (e) => {
      if (e.clientY <= 0 && !showExitIntent && cart.length === 0) {
        setShowExitIntent(true);
      }
    };
    document.addEventListener('mouseleave', handleMouseLeave);
    return () => document.removeEventListener('mouseleave', handleMouseLeave);
  }, [showExitIntent, cart]);

  // Age gate verification
  useEffect(() => {
    const verified = sessionStorage.getItem('ageVerified');
    if (verified) {
      setIsAgeVerified(true);
    }
    setCtaVariant(['A', 'B', 'C'][Math.floor(Math.random() * 3)]);
  }, []);

  // Load cart from localStorage
  useEffect(() => {
    const savedCart = localStorage.getItem('weedNearMeCart');
    if (savedCart) {
      setCart(JSON.parse(savedCart));
    }
  }, []);

  // Save cart to localStorage
  useEffect(() => {
    localStorage.setItem('weedNearMeCart', JSON.stringify(cart));
  }, [cart]);

  const handleAgeVerification = (isOver21) => {
    if (isOver21) {
      sessionStorage.setItem('ageVerified', 'true');
      setIsAgeVerified(true);
    } else {
      window.location.href = 'https://www.google.com';
    }
  };

  // Add to cart
  const addToCart = (product, quantity = 1) => {
    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      setCart(cart.map(item => 
        item.id === product.id 
          ? { ...item, quantity: item.quantity + quantity }
          : item
      ));
    } else {
      setCart([...cart, { ...product, quantity }]);
    }
    setShowNotification(true);
    setTimeout(() => setShowNotification(false), 3000);
    
    // Track analytics
    trackEvent('add_to_cart', {
      product_id: product.id,
      product_name: product.name,
      price: product.price,
      category: product.category,
      quantity
    });
  };

  // Remove from cart
  const removeFromCart = (productId) => {
    setCart(cart.filter(item => item.id !== productId));
  };

  // Update cart quantity
  const updateCartQuantity = (productId, quantity) => {
    if (quantity === 0) {
      removeFromCart(productId);
    } else {
      setCart(cart.map(item => 
        item.id === productId 
          ? { ...item, quantity }
          : item
      ));
    }
  };

  // Calculate cart total
  const cartTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const cartCount = cart.reduce((sum, item) => sum + item.quantity, 0);

  // Filter products
  const getFilteredProducts = () => {
    let filtered = products;

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(p => p.category === selectedCategory);
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(query) ||
        p.brand.toLowerCase().includes(query) ||
        p.description.toLowerCase().includes(query) ||
        (p.effects && p.effects.some(e => e.toLowerCase().includes(query)))
      );
    }

    if (selectedBrands.length > 0) {
      filtered = filtered.filter(p => selectedBrands.includes(p.brand));
    }

    if (selectedEffects.length > 0) {
      filtered = filtered.filter(p => 
        p.effects && selectedEffects.some(effect => p.effects.includes(effect))
      );
    }

    filtered = filtered.filter(p => p.price >= priceRange[0] && p.price <= priceRange[1]);

    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => b.id - a.id);
        break;
      default:
        filtered.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
    }

    return filtered;
  };

  // Age Gate Component
  if (!isAgeVerified) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center space-y-8">
          <div>
            <h1 className="text-4xl font-light tracking-wide">WeedNearMeDC</h1>
            <p className="mt-4 text-gray-600">You must be 21 or older to enter this site</p>
          </div>
          <div className="space-y-4">
            <button
              onClick={() => handleAgeVerification(true)}
              className="w-full py-4 bg-[#C79500] text-white rounded hover:bg-[#B08400] transition-colors"
            >
              I am 21 or older
            </button>
            <button
              onClick={() => handleAgeVerification(false)}
              className="w-full py-4 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
            >
              I am under 21
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Product Page Component
  const ProductPage = ({ productSlug }) => {
    const product = products.find(p => p.slug === productSlug);
    if (!product) return null;

    const relatedProducts = products
      .filter(p => p.category === product.category && p.id !== product.id)
      .slice(0, 4);

    return (
      <div className="pt-24 pb-16">
        <div className="max-w-6xl mx-auto px-4">
          {/* Breadcrumbs */}
          <div className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
            <button onClick={() => setActiveView('home')} className="hover:text-[#C79500]">
              Home
            </button>
            <ChevronRight className="w-4 h-4" />
            <button onClick={() => setActiveView('products')} className="hover:text-[#C79500]">
              Shop
            </button>
            <ChevronRight className="w-4 h-4" />
            <span className="text-gray-900">{product.name}</span>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            {/* Product Image */}
            <div>
              <div className="bg-gray-50 rounded-lg p-12 text-center">
                <div className="text-9xl mb-8">{product.image}</div>
                {product.deal && (
                  <div className="inline-flex items-center px-4 py-2 bg-[#C79500] text-white rounded-full">
                    <Zap className="w-4 h-4 mr-2" />
                    {product.deal}
                  </div>
                )}
              </div>
            </div>

            {/* Product Info */}
            <div>
              <div className="mb-4">
                <h1 className="text-3xl font-medium mb-2">{product.name}</h1>
                <p className="text-xl text-gray-500">{product.brand}</p>
              </div>

              <div className="flex items-center mb-6">
                <div className="flex mr-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className={`w-5 h-5 ${i < Math.floor(product.rating) ? 'text-[#C79500] fill-current' : 'text-gray-300'}`} />
                  ))}
                </div>
                <span className="text-gray-600">{product.rating} ({product.reviews} reviews)</span>
              </div>

              <p className="text-gray-600 mb-8">{product.description}</p>

              {/* Product Details */}
              <div className="space-y-6 mb-8">
                <div>
                  <h3 className="font-medium mb-3">Details</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Type:</span>
                      <span className="ml-2 font-medium">{product.type}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">THC:</span>
                      <span className="ml-2 font-medium">{product.thc}</span>
                    </div>
                    {product.cbd && (
                      <div>
                        <span className="text-gray-500">CBD:</span>
                        <span className="ml-2 font-medium">{product.cbd}</span>
                      </div>
                    )}
                  </div>
                </div>

                {product.effects && (
                  <div>
                    <h3 className="font-medium mb-3">Effects</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.effects.map(effect => (
                        <span key={effect} className="px-3 py-1 bg-gray-100 rounded-full text-sm">
                          {effect}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Price and Add to Cart */}
              <div className="border-t pt-8">
                <div className="flex items-end justify-between mb-6">
                  <div>
                    <span className="text-3xl font-light">${product.price}</span>
                    {product.originalPrice && (
                      <span className="text-xl text-gray-500 line-through ml-3">${product.originalPrice}</span>
                    )}
                  </div>
                  {product.inStock ? (
                    <span className="text-green-600 text-sm flex items-center">
                      <CheckCircle className="w-4 h-4 mr-1" />
                      In Stock
                    </span>
                  ) : (
                    <span className="text-red-600 text-sm">Out of Stock</span>
                  )}
                </div>

                <button
                  onClick={() => addToCart(product)}
                  className="w-full py-3 bg-[#C79500] text-white rounded-lg hover:bg-[#B08400] transition-colors"
                  disabled={!product.inStock}
                >
                  Add to Cart
                </button>
              </div>

              {/* Delivery Info */}
              <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center text-sm">
                  <Truck className="w-5 h-5 text-[#C79500] mr-3" />
                  <span>
                    Delivery available in {locations[selectedLocation].deliveryTime} • 
                    Min order {locations[selectedLocation].minOrder}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Product Grid Component
  const ProductGrid = () => {
    const filteredProducts = getFilteredProducts();

    return (
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header with filters */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-light">
                {selectedCategory === 'all' ? 'All Products' : selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)}
              </h2>
              <p className="text-gray-500 mt-1">{filteredProducts.length} products</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center px-4 py-2 border border-gray-200 rounded-lg hover:border-[#C79500] transition-colors"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </button>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 border border-gray-200 rounded-lg"
              >
                <option value="featured">Featured</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
                <option value="newest">Newest</option>
              </select>
            </div>
          </div>

          {/* Category Pills */}
          <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
            {['all', 'flower', 'edibles', 'vapes', 'concentrates', 'wellness'].map(cat => (
              <button
                key={cat}
                onClick={() => setSelectedCategory(cat)}
                className={`px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all ${
                  selectedCategory === cat 
                    ? 'bg-[#C79500] text-white' 
                    : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                {cat.charAt(0).toUpperCase() + cat.slice(1)}
                {cat !== 'all' && (
                  <span className="ml-2 text-xs opacity-75">
                    ({products.filter(p => p.category === cat).length})
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Products Grid */}
          <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredProducts.map(product => (
              <div 
                key={product.id} 
                className="bg-white border border-gray-200 rounded-lg p-6 hover:border-[#C79500] transition-all group cursor-pointer"
                onClick={() => setCurrentProductPage(product.slug)}
              >
                {product.deal && (
                  <div className="bg-[#C79500] text-white text-xs px-2 py-1 rounded-full inline-block mb-2">
                    {product.deal}
                  </div>
                )}
                <div className="text-4xl mb-4 text-center">{product.image}</div>
                <h3 className="font-medium mb-1">{product.name}</h3>
                <p className="text-sm text-gray-500 mb-2">{product.brand} • {product.type}</p>
                <div className="flex items-center mb-2">
                  <Star className="w-4 h-4 text-[#C79500] fill-current" />
                  <span className="text-sm ml-1">{product.rating} ({product.reviews})</span>
                </div>
                <div className="flex items-center justify-between mt-4">
                  <div>
                    <span className="text-xl font-light">${product.price}</span>
                    {product.originalPrice && (
                      <span className="text-sm text-gray-500 line-through ml-2">${product.originalPrice}</span>
                    )}
                  </div>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      addToCart(product);
                    }}
                    className="opacity-0 group-hover:opacity-100 bg-[#C79500] text-white px-4 py-2 rounded text-sm transition-all"
                  >
                    Add
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  };

  // Notification Component
  const Notification = () => (
    <div className="fixed top-20 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center z-50">
      <CheckCircle className="w-5 h-5 mr-2" />
      <span>Item added to cart!</span>
    </div>
  );

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm z-40 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-8">
              <h1 
                onClick={() => {
                  setActiveView('home');
                  setCurrentProductPage(null);
                  setSelectedBlogPost(null);
                }}
                className="text-2xl font-light tracking-wide cursor-pointer"
              >
                WeedNearMeDC
              </h1>
              <div className="hidden md:flex space-x-6">
                <button 
                  onClick={() => {
                    setActiveView('products');
                    setCurrentProductPage(null);
                  }}
                  className={`text-gray-600 hover:text-[#C79500] transition-colors ${
                    activeView === 'products' ? 'text-[#C79500]' : ''
                  }`}
                >
                  Shop
                </button>
                <button className="text-gray-600 hover:text-[#C79500] transition-colors">
                  Learn
                </button>
                <button className="text-gray-600 hover:text-[#C79500] transition-colors">
                  Deals
                </button>
              </div>
            </div>

            <div className="hidden md:flex items-center space-x-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 pr-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:border-[#C79500] transition-colors"
                />
                <Search className="w-4 h-4 absolute left-2.5 top-2.5 text-gray-400" />
              </div>
              <button className="text-gray-600 hover:text-[#C79500] transition-colors flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {locations[selectedLocation].title}
              </button>
              <button 
                onClick={() => setShowCannaQuest(true)}
                className="text-gray-600 hover:text-[#C79500] transition-colors flex items-center relative"
              >
                <Trophy className="w-5 h-5 mr-1" />
                <span className="hidden sm:inline">CannaQuest</span>
                {userOrderCount >= 20 && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-[#C79500] rounded-full"></div>
                )}
              </button>
              <button 
                onClick={() => cartCount > 0 && setShowCheckout(true)}
                className="relative text-gray-600 hover:text-[#C79500] transition-colors"
              >
                <ShoppingBag className="w-5 h-5" />
                {cartCount > 0 && (
                  <div className="absolute -top-2 -right-2 bg-[#C79500] text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
                    {cartCount}
                  </div>
                )}
              </button>
              <a href="tel:+12025551234" className="flex items-center text-[#C79500] hover:text-[#B08400] transition-colors">
                <Phone className="w-4 h-4 mr-2" />
                (*************
              </a>
            </div>

            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden p-2"
            >
              {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </nav>

      {/* Dynamic Content */}
      {currentProductPage ? (
        <ProductPage productSlug={currentProductPage} />
      ) : activeView === 'home' ? (
        <>
          {/* Scroll-Reactive Hero */}
          <ScrollReactiveHero 
            onCTAClick={(stage) => {
              if (stage === 'stage3') {
                setActiveView('products');
              } else {
                setActiveView('products');
              }
              trackEvent('hero_cta_click', { stage, location: selectedLocation });
            }}
            selectedLocation={selectedLocation}
            locations={locations}
          />

          {/* Location Selector */}
          <section className="py-16 px-4 bg-gray-50">
            <div className="max-w-6xl mx-auto">
              <h3 className="text-2xl font-light text-center mb-8">Choose Your Location</h3>
              <div className="grid md:grid-cols-3 gap-6">
                {Object.entries(locations).map(([key, location]) => (
                  <button
                    key={key}
                    onClick={() => setSelectedLocation(key)}
                    className={`p-8 rounded-lg border-2 transition-all ${
                      selectedLocation === key
                        ? 'border-[#C79500] bg-white shadow-lg'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                  >
                    <MapPin className={`w-8 h-8 mb-4 mx-auto ${
                      selectedLocation === key ? 'text-[#C79500]' : 'text-gray-400'
                    }`} />
                    <h3 className="text-xl font-medium mb-2">{location.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">{location.subtitle}</p>
                    <p className="text-xs text-gray-500 mb-1">{location.delivery}</p>
                    <div className="flex justify-center space-x-4 mt-4 text-xs text-gray-500">
                      <span>⏱ {location.deliveryTime}</span>
                      <span>💵 Min {location.minOrder}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </section>

          {/* Trust Signals */}
          <section className="py-16 px-4">
            <div className="max-w-5xl mx-auto">
              <div className="grid md:grid-cols-5 gap-8 text-center">
                <div>
                  <Clock className="w-12 h-12 mx-auto mb-4 text-[#C79500]" />
                  <h3 className="text-lg font-medium mb-2">Same Day</h3>
                  <p className="text-sm text-gray-600">Order by 7pm</p>
                </div>
                <div>
                  <Shield className="w-12 h-12 mx-auto mb-4 text-[#C79500]" />
                  <h3 className="text-lg font-medium mb-2">Licensed</h3>
                  <p className="text-sm text-gray-600">Fully compliant</p>
                </div>
                <div>
                  <Package className="w-12 h-12 mx-auto mb-4 text-[#C79500]" />
                  <h3 className="text-lg font-medium mb-2">Discreet</h3>
                  <p className="text-sm text-gray-600">Private delivery</p>
                </div>
                <div>
                  <TrendingUp className="w-12 h-12 mx-auto mb-4 text-[#C79500]" />
                  <h3 className="text-lg font-medium mb-2">Best Prices</h3>
                  <p className="text-sm text-gray-600">Price match</p>
                </div>
                <div>
                  <Heart className="w-12 h-12 mx-auto mb-4 text-[#C79500]" />
                  <h3 className="text-lg font-medium mb-2">Lab Tested</h3>
                  <p className="text-sm text-gray-600">Safe & pure</p>
                </div>
              </div>
            </div>
          </section>

          {/* Popular Products Preview */}
          <section className="py-16 px-4 bg-gray-50">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-light mb-4">Popular Products</h2>
                <p className="text-gray-600">Top-rated items from our collection</p>
              </div>
              <div className="grid md:grid-cols-4 gap-6">
                {products.filter(p => p.featured || p.rating >= 4.8).slice(0, 8).map(product => (
                  <div 
                    key={product.id}
                    className="bg-white border border-gray-200 rounded-lg p-6 cursor-pointer hover:border-[#C79500] transition-all"
                    onClick={() => setCurrentProductPage(product.slug)}
                  >
                    {product.deal && (
                      <div className="bg-[#C79500] text-white text-xs px-2 py-1 rounded-full inline-block mb-2">
                        {product.deal}
                      </div>
                    )}
                    <div className="text-4xl mb-4 text-center">{product.image}</div>
                    <h3 className="font-medium mb-1">{product.name}</h3>
                    <p className="text-sm text-gray-500 mb-2">{product.brand}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-light">${product.price}</span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          addToCart(product);
                        }}
                        className="text-[#C79500] hover:text-[#B08400] text-sm"
                      >
                        Add +
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="text-center mt-8">
                <button
                  onClick={() => setActiveView('products')}
                  className="inline-flex items-center text-[#C79500] hover:text-[#B08400] transition-colors"
                >
                  View All Products <ArrowRight className="w-4 h-4 ml-2" />
                </button>
              </div>
            </div>
          </section>
        </>
      ) : activeView === 'products' ? (
        <div className="pt-20">
          <ProductGrid />
        </div>
      ) : null}

      {/* Footer */}
      <footer className="bg-white border-t border-gray-100 py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-5 gap-8">
            <div className="md:col-span-2">
              <h3 className="text-lg font-light tracking-wide mb-4">WeedNearMeDC</h3>
              <p className="text-sm text-gray-600 mb-4">
                Your trusted cannabis delivery partner in the DMV area. Licensed and compliant in DC, MD & VA.
              </p>
              <div className="flex items-center mb-4 text-sm text-gray-600">
                <Star className="w-4 h-4 text-[#C79500] fill-current mr-1" />
                <span>4.9 • 2,847 reviews</span>
              </div>
              <div className="flex">
                <input
                  type="tel"
                  placeholder="Phone for deals"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-200 rounded-l text-sm"
                />
                <button 
                  onClick={() => alert(`SMS notifications enabled for ${phoneNumber}`)}
                  className="px-4 py-2 bg-[#C79500] text-white rounded-r text-sm hover:bg-[#B08400] transition-colors"
                >
                  Join
                </button>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-4">Washington DC</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-[#C79500]">Get Medical Card</a></li>
                <li><a href="#" className="hover:text-[#C79500]">Self-Certification</a></li>
                <li><a href="#" className="hover:text-[#C79500]">Georgetown</a></li>
                <li><a href="#" className="hover:text-[#C79500]">U Street</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-4">Maryland</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-[#C79500]">Recreational Info</a></li>
                <li><a href="#" className="hover:text-[#C79500]">Baltimore</a></li>
                <li><a href="#" className="hover:text-[#C79500]">Silver Spring</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-4">Virginia</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-[#C79500]">Medical Guide</a></li>
                <li><a href="#" className="hover:text-[#C79500]">Richmond</a></li>
                <li><a href="#" className="hover:text-[#C79500]">Alexandria</a></li>
              </ul>
            </div>
          </div>

          <div className="mt-12 pt-8 border-t border-gray-100">
            <div className="flex flex-wrap justify-between items-center text-sm text-gray-500">
              <p>© 2025 WeedNearMeDC. All rights reserved.</p>
              <div className="flex space-x-6">
                <a href="#" className="hover:text-gray-700">Privacy</a>
                <a href="#" className="hover:text-gray-700">Terms</a>
                <a href="#" className="hover:text-gray-700">Compliance</a>
              </div>
            </div>
            <p className="text-xs text-gray-400 mt-4">
              21+ Only. Not for sale or use by minors. Keep out of reach of children and pets.
            </p>
          </div>
        </div>
      </footer>

      {/* Modals and Popups */}
      {showNotification && <Notification />}
      {showCannaQuest && (
        <CannaQuest 
          isUnlocked={userOrderCount >= 20}
          userOrderCount={userOrderCount}
          onClose={() => setShowCannaQuest(false)}
        />
      )}
    </div>
  );
};

export default WeedNearMeDC;