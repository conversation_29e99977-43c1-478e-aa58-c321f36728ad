"use client"

import { trpc } from '@/lib/trpc/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, Heart, ShoppingCart, Leaf } from 'lucide-react'
import { PRODUCT_CATEGORIES } from '@/constants'
import { toast } from 'sonner'

interface ProductGridProps {
  category?: string
  state?: 'DC' | 'MD' | 'VA'
  featured?: boolean
  limit?: number
}

export function ProductGrid({ category, state = 'MD', featured, limit = 20 }: ProductGridProps) {
  const { data, isLoading, error } = trpc.products.getAll.useQuery({
    category,
    state,
    featured,
    limit
  })

  const addToCartMutation = trpc.cart.add.useMutation({
    onSuccess: () => {
      toast.success('Added to cart!')
    },
    onError: (error) => {
      toast.error(error.message)
    }
  })

  const handleAddToCart = (productId: number) => {
    addToCartMutation.mutate({
      productId,
      quantity: 1
    })
  }

  if (isLoading) {
    return (
      <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="aspect-square bg-gray-200 rounded-t-lg" />
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4" />
              <div className="h-3 bg-gray-200 rounded w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded" />
                <div className="h-3 bg-gray-200 rounded w-2/3" />
                <div className="h-8 bg-gray-200 rounded" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Error loading products: {error.message}</p>
      </div>
    )
  }

  if (!data?.products.length) {
    return (
      <div className="text-center py-12">
        <Leaf className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
        <p className="text-gray-600">Try adjusting your filters or check back later.</p>
      </div>
    )
  }

  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {data.products.map((product) => (
        <Card key={product.id} className="group hover:shadow-lg transition-shadow">
          <div className="relative">
            <div className="aspect-square bg-gray-200 rounded-t-lg flex items-center justify-center">
              <Leaf className="h-16 w-16 text-gray-400" />
            </div>
            
            {/* Badges */}
            <div className="absolute top-2 left-2 flex flex-col gap-1">
              {product.featured && (
                <Badge className="bg-green-600 text-white">Featured</Badge>
              )}
              {product.onSale && (
                <Badge className="bg-red-600 text-white">Sale</Badge>
              )}
              {product.labTested && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  Lab Tested
                </Badge>
              )}
            </div>

            {/* Favorite Button */}
            <Button
              size="sm"
              variant="outline"
              className="absolute top-2 right-2 h-8 w-8 p-0 bg-white/80 backdrop-blur-sm"
            >
              <Heart className="h-4 w-4" />
            </Button>
          </div>
          
          <CardHeader className="pb-2">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-lg group-hover:text-green-600 transition-colors line-clamp-2">
                  {product.name}
                </CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <span>
                    {PRODUCT_CATEGORIES[product.category as keyof typeof PRODUCT_CATEGORIES]?.label}
                  </span>
                  {product.weight && (
                    <>
                      <span>•</span>
                      <span>{product.weight}</span>
                    </>
                  )}
                </CardDescription>
              </div>
              <div className="text-right ml-2">
                <div className="font-bold text-lg">
                  ${(product.price / 100).toFixed(2)}
                </div>
                {product.compareAtPrice && (
                  <div className="text-sm text-gray-500 line-through">
                    ${(product.compareAtPrice / 100).toFixed(2)}
                  </div>
                )}
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            {/* Rating */}
            <div className="flex items-center gap-2 mb-3">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium">{product.rating.toFixed(1)}</span>
              </div>
              <span className="text-sm text-gray-500">
                ({product.reviewCount} reviews)
              </span>
            </div>
            
            {/* Product Details */}
            <div className="space-y-2 mb-4">
              {product.thcContent && (
                <div className="text-sm">
                  <span className="font-medium">THC:</span> {product.thcContent}
                </div>
              )}
              {product.strain && (
                <div className="text-sm">
                  <span className="font-medium">Type:</span> {product.strain}
                </div>
              )}
            </div>
            
            {/* Effects */}
            {product.effects.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-4">
                {product.effects.slice(0, 3).map((effect) => (
                  <Badge key={effect} variant="secondary" className="text-xs">
                    {effect}
                  </Badge>
                ))}
                {product.effects.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{product.effects.length - 3} more
                  </Badge>
                )}
              </div>
            )}
            
            {/* Stock Status */}
            <div className="mb-4">
              {product.inStock ? (
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <div className="w-2 h-2 bg-green-600 rounded-full" />
                  <span>In Stock ({product.stockCount} available)</span>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-sm text-red-600">
                  <div className="w-2 h-2 bg-red-600 rounded-full" />
                  <span>Out of Stock</span>
                </div>
              )}
            </div>
            
            {/* Add to Cart Button */}
            <Button 
              className="w-full bg-green-600 hover:bg-green-700"
              disabled={!product.inStock || addToCartMutation.isPending}
              onClick={() => handleAddToCart(product.id)}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {addToCartMutation.isPending ? 'Adding...' : 'Add to Cart'}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
