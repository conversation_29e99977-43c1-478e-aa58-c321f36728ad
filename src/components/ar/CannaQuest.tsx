'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Scan, Trophy, Gift, MapPin, Star, Zap, Target, CheckCircle, X } from 'lucide-react';

interface QuestObjective {
  id: string;
  title: string;
  description: string;
  location: {
    lat: number;
    lng: number;
    address: string;
  };
  reward: {
    type: 'points' | 'discount' | 'product';
    value: number;
    description: string;
  };
  completed: boolean;
  arMarker?: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface UserProgress {
  level: number;
  totalPoints: number;
  completedQuests: string[];
  currentStreak: number;
  badges: string[];
}

const QUEST_OBJECTIVES: QuestObjective[] = [
  {
    id: 'dispensary-check-in',
    title: 'Dispensary Explorer',
    description: 'Check in at 3 different dispensary locations',
    location: { lat: 38.9072, lng: -77.0369, address: 'Various DC locations' },
    reward: { type: 'points', value: 500, description: '500 Quest Points' },
    completed: false,
    difficulty: 'easy'
  },
  {
    id: 'strain-collector',
    title: 'Strain Collector',
    description: 'Scan QR codes on 5 different cannabis strains',
    location: { lat: 38.9072, lng: -77.0369, address: 'Any participating dispensary' },
    reward: { type: 'discount', value: 15, description: '15% off next order' },
    completed: false,
    difficulty: 'medium'
  },
  {
    id: 'neighborhood-champion',
    title: 'Neighborhood Champion',
    description: 'Complete delivery orders in 3 different neighborhoods',
    location: { lat: 38.9072, lng: -77.0369, address: 'Georgetown, U Street, Capitol Hill' },
    reward: { type: 'product', value: 1, description: 'Free premium eighth' },
    completed: false,
    difficulty: 'hard'
  },
  {
    id: 'review-master',
    title: 'Review Master',
    description: 'Leave detailed reviews for 10 products',
    location: { lat: 38.9072, lng: -77.0369, address: 'Online platform' },
    reward: { type: 'points', value: 300, description: '300 Quest Points' },
    completed: false,
    difficulty: 'easy'
  },
  {
    id: 'loyalty-legend',
    title: 'Loyalty Legend',
    description: 'Refer 5 friends to WeedNearMeDC',
    location: { lat: 38.9072, lng: -77.0369, address: 'Social sharing' },
    reward: { type: 'discount', value: 25, description: '25% off + friend gets 20%' },
    completed: false,
    difficulty: 'hard'
  }
];

const BADGES = [
  { id: 'first-quest', name: 'First Quest', icon: '🎯', description: 'Complete your first quest' },
  { id: 'streak-master', name: 'Streak Master', icon: '🔥', description: 'Complete 7 quests in a row' },
  { id: 'explorer', name: 'Explorer', icon: '🗺️', description: 'Visit 10 different locations' },
  { id: 'reviewer', name: 'Reviewer', icon: '⭐', description: 'Leave 50 product reviews' },
  { id: 'social-butterfly', name: 'Social Butterfly', icon: '👥', description: 'Refer 10 friends' },
  { id: 'legend', name: 'Legend', icon: '👑', description: 'Reach level 25' }
];

interface CannaQuestProps {
  isUnlocked: boolean;
  userOrderCount: number;
  onClose: () => void;
}

export default function CannaQuest({ isUnlocked, userOrderCount, onClose }: CannaQuestProps) {
  const [userProgress, setUserProgress] = useState<UserProgress>({
    level: 1,
    totalPoints: 0,
    completedQuests: [],
    currentStreak: 0,
    badges: []
  });
  
  const [activeQuest, setActiveQuest] = useState<QuestObjective | null>(null);
  const [showARScanner, setShowARScanner] = useState(false);
  const [cameraPermission, setCameraPermission] = useState<'granted' | 'denied' | 'pending'>('pending');
  const [currentObjectives, setCurrentObjectives] = useState<QuestObjective[]>(QUEST_OBJECTIVES);
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [lastReward, setLastReward] = useState<QuestObjective['reward'] | null>(null);
  const [questPulse, setQuestPulse] = useState<string | null>(null);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Check if CannaQuest is unlocked (20+ orders)
  useEffect(() => {
    if (userOrderCount >= 20) {
      // Load user progress from localStorage
      const savedProgress = localStorage.getItem('cannaquest-progress');
      if (savedProgress) {
        setUserProgress(JSON.parse(savedProgress));
      }
    }
  }, [userOrderCount]);

  // Save progress to localStorage
  useEffect(() => {
    localStorage.setItem('cannaquest-progress', JSON.stringify(userProgress));
  }, [userProgress]);

  // Request camera permissions
  const requestCameraPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      });
      setCameraPermission('granted');
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      setCameraPermission('denied');
      console.error('Camera permission denied:', error);
    }
  };

  // Complete a quest
  const completeQuest = (questId: string) => {
    const quest = currentObjectives.find(q => q.id === questId);
    if (!quest || userProgress.completedQuests.includes(questId)) return;

    const newProgress = {
      ...userProgress,
      totalPoints: userProgress.totalPoints + (quest.reward.type === 'points' ? quest.reward.value : 100),
      completedQuests: [...userProgress.completedQuests, questId],
      currentStreak: userProgress.currentStreak + 1,
      level: Math.floor((userProgress.totalPoints + 100) / 1000) + 1
    };

    // Check for new badges
    const newBadges = [...userProgress.badges];
    if (newProgress.completedQuests.length === 1 && !newBadges.includes('first-quest')) {
      newBadges.push('first-quest');
    }
    if (newProgress.currentStreak >= 7 && !newBadges.includes('streak-master')) {
      newBadges.push('streak-master');
    }

    setUserProgress({ ...newProgress, badges: newBadges });
    setCurrentObjectives(prev => 
      prev.map(q => q.id === questId ? { ...q, completed: true } : q)
    );
    
    setLastReward(quest.reward);
    setShowRewardModal(true);
    setQuestPulse(questId);
    
    // Remove pulse after animation
    setTimeout(() => setQuestPulse(null), 1000);
  };

  // Mock AR scanner component
  const ARScanner = () => (
    <div className="fixed inset-0 bg-black z-50 flex flex-col">
      <div className="flex-1 relative">
        <video 
          ref={videoRef}
          className="w-full h-full object-cover"
          autoPlay
          playsInline
          muted
        />
        <canvas 
          ref={canvasRef}
          className="absolute inset-0 w-full h-full"
        />
        
        {/* AR Overlay */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="relative">
            <div className="w-64 h-64 border-2 border-[#C79500] rounded-lg">
              <div className="absolute top-0 left-0 w-8 h-8 border-l-4 border-t-4 border-[#C79500]"></div>
              <div className="absolute top-0 right-0 w-8 h-8 border-r-4 border-t-4 border-[#C79500]"></div>
              <div className="absolute bottom-0 left-0 w-8 h-8 border-l-4 border-b-4 border-[#C79500]"></div>
              <div className="absolute bottom-0 right-0 w-8 h-8 border-r-4 border-b-4 border-[#C79500]"></div>
            </div>
            
            <motion.div 
              className="absolute inset-0 bg-[#C79500]/20 rounded-lg"
              animate={{ opacity: [0.2, 0.5, 0.2] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </div>
        </div>

        {/* Instructions */}
        <div className="absolute top-4 left-4 right-4 bg-black/80 text-white p-4 rounded-lg">
          <p className="text-center">
            Point your camera at a QR code or product label to scan
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-black/90 p-4 flex justify-between items-center">
        <button 
          onClick={() => setShowARScanner(false)}
          className="text-white p-2"
        >
          <X className="w-6 h-6" />
        </button>
        
        <div className="flex space-x-4">
          <button 
            onClick={() => completeQuest('strain-collector')}
            className="px-6 py-2 bg-[#C79500] text-white rounded-lg"
          >
            <Scan className="w-5 h-5 mr-2 inline" />
            Scan
          </button>
        </div>
      </div>
    </div>
  );

  // Reward modal
  const RewardModal = () => (
    <motion.div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div 
        className="bg-white rounded-2xl p-8 max-w-md w-full text-center"
        initial={{ scale: 0.8, y: 50 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.8, y: 50 }}
      >
        <div className="text-6xl mb-4">🎉</div>
        <h3 className="text-2xl font-bold mb-2">Quest Complete!</h3>
        <p className="text-gray-600 mb-6">
          You've earned: {lastReward?.description}
        </p>
        
        <div className="bg-gradient-to-r from-[#C79500] to-[#D4A617] text-white p-4 rounded-lg mb-6">
          <div className="text-2xl font-bold">{lastReward?.value}</div>
          <div className="text-sm">
            {lastReward?.type === 'points' ? 'Quest Points' : 
             lastReward?.type === 'discount' ? '% Discount' : 'Free Product'}
          </div>
        </div>

        <button 
          onClick={() => setShowRewardModal(false)}
          className="w-full py-3 bg-[#C79500] text-white rounded-lg hover:bg-[#B08400] transition-colors"
        >
          Claim Reward
        </button>
      </motion.div>
    </motion.div>
  );

  if (!isUnlocked) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <motion.div 
          className="bg-white rounded-2xl p-8 max-w-md w-full text-center"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
        >
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold mb-4">CannaQuest Locked</h2>
          <p className="text-gray-600 mb-6">
            Complete 20 orders to unlock the AR scavenger hunt game and start earning exclusive rewards!
          </p>
          
          <div className="bg-gray-100 rounded-lg p-4 mb-6">
            <div className="text-sm text-gray-500 mb-2">Progress</div>
            <div className="flex items-center justify-between">
              <div className="text-lg font-bold">{userOrderCount}/20</div>
              <div className="text-sm text-gray-500">Orders</div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-[#C79500] h-2 rounded-full transition-all duration-300"
                style={{ width: `${(userOrderCount / 20) * 100}%` }}
              />
            </div>
          </div>

          <button 
            onClick={onClose}
            className="w-full py-3 bg-[#C79500] text-white rounded-lg hover:bg-[#B08400] transition-colors"
          >
            Continue Shopping
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <motion.div 
          className="bg-white rounded-2xl max-w-4xl w-full h-[90vh] flex flex-col"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-[#005F4E] to-[#C79500] text-white p-6 rounded-t-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Trophy className="w-8 h-8 mr-3" />
                <div>
                  <h2 className="text-2xl font-bold">CannaQuest</h2>
                  <p className="text-white/90">AR Scavenger Hunt</p>
                </div>
              </div>
              <button onClick={onClose} className="text-white hover:text-white/80">
                <X className="w-6 h-6" />
              </button>
            </div>
            
            {/* Progress Stats */}
            <div className="grid grid-cols-4 gap-4 mt-6">
              <div className="text-center">
                <div className="text-2xl font-bold">{userProgress.level}</div>
                <div className="text-sm text-white/80">Level</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{userProgress.totalPoints}</div>
                <div className="text-sm text-white/80">Points</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{userProgress.completedQuests.length}</div>
                <div className="text-sm text-white/80">Quests</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{userProgress.currentStreak}</div>
                <div className="text-sm text-white/80">Streak</div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Active Quests */}
              <div>
                <h3 className="text-xl font-bold mb-4">Active Quests</h3>
                <div className="space-y-4">
                  {currentObjectives.filter(q => !q.completed).map(quest => (
                    <motion.div 
                      key={quest.id}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        activeQuest?.id === quest.id 
                          ? 'border-[#C79500] bg-[#C79500]/10' 
                          : 'border-gray-200 hover:border-[#C79500]/50'
                      } ${questPulse === quest.id ? 'animate-pulse' : ''}`}
                      onClick={() => setActiveQuest(quest)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center mb-2">
                            <Target className="w-5 h-5 text-[#C79500] mr-2" />
                            <span className="font-medium">{quest.title}</span>
                            <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                              quest.difficulty === 'easy' ? 'bg-green-100 text-green-600' :
                              quest.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                              'bg-red-100 text-red-600'
                            }`}>
                              {quest.difficulty}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{quest.description}</p>
                          <div className="flex items-center text-sm text-[#C79500]">
                            <Gift className="w-4 h-4 mr-1" />
                            <span>{quest.reward.description}</span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Quest Details & Actions */}
              <div>
                <h3 className="text-xl font-bold mb-4">Quest Details</h3>
                {activeQuest ? (
                  <div className="border-2 border-[#C79500] rounded-lg p-6">
                    <h4 className="text-lg font-medium mb-2">{activeQuest.title}</h4>
                    <p className="text-gray-600 mb-4">{activeQuest.description}</p>
                    
                    <div className="bg-gray-50 p-4 rounded-lg mb-4">
                      <div className="flex items-center mb-2">
                        <MapPin className="w-4 h-4 text-gray-500 mr-2" />
                        <span className="text-sm">{activeQuest.location.address}</span>
                      </div>
                      <div className="flex items-center">
                        <Gift className="w-4 h-4 text-[#C79500] mr-2" />
                        <span className="text-sm font-medium">{activeQuest.reward.description}</span>
                      </div>
                    </div>

                    <div className="flex space-x-3">
                      <button 
                        onClick={() => {
                          setShowARScanner(true);
                          requestCameraPermission();
                        }}
                        className="flex-1 py-2 bg-[#C79500] text-white rounded-lg hover:bg-[#B08400] transition-colors flex items-center justify-center"
                      >
                        <Camera className="w-4 h-4 mr-2" />
                        Start AR Scan
                      </button>
                      <button 
                        onClick={() => completeQuest(activeQuest.id)}
                        className="px-4 py-2 border border-[#C79500] text-[#C79500] rounded-lg hover:bg-[#C79500]/10 transition-colors"
                      >
                        <CheckCircle className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="border-2 border-gray-200 rounded-lg p-6 text-center text-gray-500">
                    <Target className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>Select a quest to view details</p>
                  </div>
                )}

                {/* Badges */}
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-3">Your Badges</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {BADGES.map(badge => (
                      <div 
                        key={badge.id}
                        className={`p-2 rounded-lg text-center ${
                          userProgress.badges.includes(badge.id) 
                            ? 'bg-[#C79500]/10 border-2 border-[#C79500]' 
                            : 'bg-gray-100 border-2 border-gray-200'
                        }`}
                      >
                        <div className="text-2xl mb-1">{badge.icon}</div>
                        <div className="text-xs font-medium">{badge.name}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* AR Scanner */}
      <AnimatePresence>
        {showARScanner && <ARScanner />}
      </AnimatePresence>

      {/* Reward Modal */}
      <AnimatePresence>
        {showRewardModal && <RewardModal />}
      </AnimatePresence>
    </>
  );
}