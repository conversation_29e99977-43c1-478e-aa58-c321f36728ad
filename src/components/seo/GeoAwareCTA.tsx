'use client';

import React, { useState, useEffect } from 'react';
import { MapPin, Clock, Truck } from 'lucide-react';

interface GeoAwareCTAProps {
  area: string;
  city: string;
  state: string;
  deliveryAvailable: boolean;
}

export default function GeoAwareCTA({ area, city, state, deliveryAvailable }: GeoAwareCTAProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [deliveryStatus, setDeliveryStatus] = useState<'open' | 'closed' | 'opening-soon'>('open');

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const hour = currentTime.getHours();
    if (hour >= 10 && hour < 22) {
      setDeliveryStatus('open');
    } else if (hour >= 8 && hour < 10) {
      setDeliveryStatus('opening-soon');
    } else {
      setDeliveryStatus('closed');
    }
  }, [currentTime]);

  const getDeliveryMessage = () => {
    const hour = currentTime.getHours();
    const minute = currentTime.getMinutes();
    
    switch (deliveryStatus) {
      case 'open':
        return `Delivery open in ${area} until 10 PM`;
      case 'opening-soon':
        const minutesUntilOpen = (10 - hour) * 60 - minute;
        return `Delivery opens in ${area} in ${minutesUntilOpen} minutes`;
      case 'closed':
        return `Delivery closed in ${area} • Opens at 10 AM`;
      default:
        return `Delivery available in ${area}`;
    }
  };

  const getStatusColor = () => {
    switch (deliveryStatus) {
      case 'open':
        return 'text-green-600';
      case 'opening-soon':
        return 'text-yellow-600';
      case 'closed':
        return 'text-gray-500';
      default:
        return 'text-gray-600';
    }
  };

  if (!deliveryAvailable) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-center">
          <MapPin className="w-5 h-5 text-yellow-600 mr-2" />
          <span className="text-yellow-800">
            Delivery not yet available in {area}. Check back soon!
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Real-time delivery status */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${
              deliveryStatus === 'open' ? 'bg-green-500' : 
              deliveryStatus === 'opening-soon' ? 'bg-yellow-500' : 'bg-gray-400'
            }`} />
            <span className={`text-sm ${getStatusColor()}`}>
              {getDeliveryMessage()}
            </span>
          </div>
          <Clock className="w-4 h-4 text-white/60" />
        </div>
      </div>

      {/* CTA Button */}
      <button
        className={`w-full py-4 px-6 rounded-2xl font-medium transition-all transform hover:scale-105 ${
          deliveryStatus === 'open' 
            ? 'bg-gradient-to-r from-[#C79500] to-[#D4A617] text-white hover:from-[#B08400] hover:to-[#C79500] shadow-lg hover:shadow-xl' 
            : 'bg-white/20 text-white/60 cursor-not-allowed'
        }`}
        disabled={deliveryStatus !== 'open'}
      >
        <div className="flex items-center justify-center">
          <Truck className="w-5 h-5 mr-2" />
          {deliveryStatus === 'open' 
            ? `Order Now - Delivery to ${area}` 
            : `Delivery ${deliveryStatus === 'opening-soon' ? 'Opening Soon' : 'Closed'}`}
        </div>
        {deliveryStatus === 'open' && (
          <div className="text-sm mt-1 text-white/90">
            2-4 hour delivery • $100 minimum
          </div>
        )}
      </button>

      {/* Additional info */}
      <div className="text-center text-white/80 text-sm">
        <p>
          Serving {area}, {city}, {state} • Licensed & Compliant • 4.9⭐ Rating
        </p>
      </div>
    </div>
  );
}