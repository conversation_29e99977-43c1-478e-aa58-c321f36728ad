import { APP_CONFIG, STATES } from "@/constants"

interface SchemaMarkupProps {
  type: 'organization' | 'product' | 'localBusiness' | 'breadcrumb'
  data?: any
}

export function SchemaMarkup({ type, data }: SchemaMarkupProps) {
  const getSchema = () => {
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "WeedNearMeDC",
          "description": "Premium cannabis delivery service in Washington DC, Maryland, and Virginia",
          "url": APP_CONFIG.url,
          "logo": `${APP_CONFIG.url}/images/logo.png`,
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": APP_CONFIG.supportPhone,
            "contactType": "customer service",
            "email": APP_CONFIG.supportEmail,
            "availableLanguage": "English"
          },
          "address": {
            "@type": "PostalAddress",
            "addressRegion": "DC, MD, VA",
            "addressCountry": "US"
          },
          "sameAs": [
            "https://www.facebook.com/weednearmedc",
            "https://www.instagram.com/weednearmedc",
            "https://www.twitter.com/weednearmedc"
          ],
          "areaServed": [
            {
              "@type": "State",
              "name": "Washington DC"
            },
            {
              "@type": "State", 
              "name": "Maryland"
            },
            {
              "@type": "State",
              "name": "Virginia"
            }
          ]
        }

      case 'localBusiness':
        return {
          "@context": "https://schema.org",
          "@type": "LocalBusiness",
          "name": "WeedNearMeDC",
          "description": "Licensed cannabis delivery service providing premium flower, edibles, vapes, and concentrates",
          "url": APP_CONFIG.url,
          "telephone": APP_CONFIG.supportPhone,
          "email": APP_CONFIG.supportEmail,
          "priceRange": "$$",
          "currenciesAccepted": "USD",
          "paymentAccepted": "Cash, Credit Card, Debit Card",
          "openingHours": `Mo-Su ${APP_CONFIG.businessHours.start}-${APP_CONFIG.businessHours.end}`,
          "serviceArea": {
            "@type": "GeoCircle",
            "geoMidpoint": {
              "@type": "GeoCoordinates",
              "latitude": 38.9072,
              "longitude": -77.0369
            },
            "geoRadius": `${APP_CONFIG.maxDeliveryDistance} miles`
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Cannabis Products",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Product",
                  "name": "Cannabis Flower",
                  "category": "Cannabis"
                }
              },
              {
                "@type": "Offer", 
                "itemOffered": {
                  "@type": "Product",
                  "name": "Cannabis Edibles",
                  "category": "Cannabis"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Product", 
                  "name": "Cannabis Vapes",
                  "category": "Cannabis"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Product",
                  "name": "Cannabis Concentrates", 
                  "category": "Cannabis"
                }
              }
            ]
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "1247",
            "bestRating": "5",
            "worstRating": "1"
          }
        }

      case 'product':
        if (!data) return null
        return {
          "@context": "https://schema.org",
          "@type": "Product",
          "name": data.name,
          "description": data.description,
          "brand": {
            "@type": "Brand",
            "name": data.brand || "WeedNearMeDC"
          },
          "category": data.category,
          "sku": data.id?.toString(),
          "offers": {
            "@type": "Offer",
            "price": (data.price / 100).toFixed(2),
            "priceCurrency": "USD",
            "availability": data.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
            "seller": {
              "@type": "Organization",
              "name": "WeedNearMeDC"
            },
            "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
          },
          "aggregateRating": data.rating ? {
            "@type": "AggregateRating", 
            "ratingValue": data.rating.toString(),
            "reviewCount": data.reviewCount?.toString() || "0",
            "bestRating": "5",
            "worstRating": "1"
          } : undefined,
          "additionalProperty": [
            data.thcContent ? {
              "@type": "PropertyValue",
              "name": "THC Content",
              "value": data.thcContent
            } : null,
            data.cbdContent ? {
              "@type": "PropertyValue", 
              "name": "CBD Content",
              "value": data.cbdContent
            } : null,
            data.strain ? {
              "@type": "PropertyValue",
              "name": "Strain Type", 
              "value": data.strain
            } : null,
            data.weight ? {
              "@type": "PropertyValue",
              "name": "Weight",
              "value": data.weight
            } : null
          ].filter(Boolean)
        }

      case 'breadcrumb':
        if (!data?.items) return null
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": data.items.map((item: any, index: number) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": `${APP_CONFIG.url}${item.url}`
          }))
        }

      default:
        return null
    }
  }

  const schema = getSchema()
  
  if (!schema) return null

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema, null, 2)
      }}
    />
  )
}
