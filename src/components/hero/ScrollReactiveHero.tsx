'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Search, MapPin, Zap, ChevronDown } from 'lucide-react';

interface VideoStage {
  id: string;
  src: string;
  title: string;
  subtitle: string;
  cta: string;
  scrollDepth: number; // vh units when video should activate
}

const videoStages: VideoStage[] = [
  {
    id: 'stage1',
    src: '/videos/hero-stage-1.mp4',
    title: 'Premium Cannabis',
    subtitle: 'Delivered Today',
    cta: 'Check Delivery Availability',
    scrollDepth: 0
  },
  {
    id: 'stage2',
    src: '/videos/hero-stage-2.mp4',
    title: 'Lab-Tested Quality',
    subtitle: 'Every Product Verified',
    cta: 'Browse Premium Selection',
    scrollDepth: 15
  },
  {
    id: 'stage3',
    src: '/videos/hero-stage-3.mp4',
    title: 'Same-Day Delivery',
    subtitle: 'Discreet & Professional',
    cta: 'Track Your Order',
    scrollDepth: 30
  },
  {
    id: 'stage4',
    src: '/videos/hero-stage-4.mp4',
    title: 'DMV Licensed',
    subtitle: 'Fully Compliant',
    cta: 'Get Started Now',
    scrollDepth: 45
  }
];

interface ScrollReactiveHeroProps {
  onCTAClick: (stage: string) => void;
  selectedLocation: string;
  locations: any;
}

export default function ScrollReactiveHero({ 
  onCTAClick, 
  selectedLocation, 
  locations 
}: ScrollReactiveHeroProps) {
  const [currentStage, setCurrentStage] = useState(0);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);
  
  const { scrollY } = useScroll();
  const { ref: heroRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: false
  });

  // Transform values based on scroll
  const y = useTransform(scrollY, [0, 1000], [0, -200]);
  const opacity = useTransform(scrollY, [0, 400], [1, 0]);
  const scale = useTransform(scrollY, [0, 500], [1, 1.1]);
  
  // Smooth spring animations
  const springY = useSpring(y, { stiffness: 400, damping: 40 });
  const springOpacity = useSpring(opacity, { stiffness: 400, damping: 40 });
  const springScale = useSpring(scale, { stiffness: 400, damping: 40 });

  // Calculate scroll depth and determine current stage
  useEffect(() => {
    const updateStage = () => {
      if (!containerRef.current) return;
      
      const scrollTop = window.scrollY;
      const windowHeight = window.innerHeight;
      const scrollDepthVh = (scrollTop / windowHeight) * 100;
      
      // Find the appropriate stage based on scroll depth
      let newStage = 0;
      for (let i = videoStages.length - 1; i >= 0; i--) {
        if (scrollDepthVh >= videoStages[i].scrollDepth) {
          newStage = i;
          break;
        }
      }
      
      if (newStage !== currentStage) {
        setCurrentStage(newStage);
        
        // Preload next video
        if (newStage < videoStages.length - 1) {
          const nextVideo = videoRefs.current[newStage + 1];
          if (nextVideo && nextVideo.readyState < 2) {
            nextVideo.load();
          }
        }
      }
    };

    const throttledUpdate = throttle(updateStage, 16); // ~60fps
    window.addEventListener('scroll', throttledUpdate, { passive: true });
    
    return () => window.removeEventListener('scroll', throttledUpdate);
  }, [currentStage]);

  // Preload and manage video playback
  useEffect(() => {
    const currentVideo = videoRefs.current[currentStage];
    if (currentVideo) {
      currentVideo.currentTime = 0;
      currentVideo.play().catch(console.error);
      
      // Pause other videos
      videoRefs.current.forEach((video, index) => {
        if (video && index !== currentStage) {
          video.pause();
        }
      });
    }
  }, [currentStage]);

  // Preload first video
  useEffect(() => {
    const firstVideo = videoRefs.current[0];
    if (firstVideo) {
      firstVideo.addEventListener('loadeddata', () => setIsVideoLoaded(true));
      firstVideo.load();
    }
  }, []);

  const handleVideoRef = (index: number) => (el: HTMLVideoElement | null) => {
    videoRefs.current[index] = el;
  };

  const currentVideoStage = videoStages[currentStage];

  return (
    <div 
      ref={containerRef}
      className="relative min-h-[200vh] overflow-hidden"
    >
      {/* Sticky Video Container */}
      <div 
        ref={heroRef}
        className="sticky top-0 h-screen w-full flex items-center justify-center"
      >
        {/* Background Videos */}
        <div className="absolute inset-0 w-full h-full">
          {videoStages.map((stage, index) => (
            <motion.video
              key={stage.id}
              ref={handleVideoRef(index)}
              className="absolute inset-0 w-full h-full object-cover"
              src={stage.src}
              muted
              loop
              playsInline
              preload={index === 0 ? 'auto' : 'metadata'}
              style={{
                opacity: index === currentStage ? 1 : 0,
                zIndex: index === currentStage ? 1 : 0
              }}
              initial={{ opacity: 0, scale: 1.05 }}
              animate={{ 
                opacity: index === currentStage ? 1 : 0,
                scale: index === currentStage ? 1 : 1.05
              }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
            />
          ))}
          
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/60 z-10" />
          
          {/* Emerald Accent Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#005F4E]/30 to-transparent z-10" />
        </div>

        {/* Content Overlay */}
        <motion.div 
          className="relative z-20 text-center text-white px-4 max-w-5xl mx-auto"
          style={{ 
            y: springY, 
            opacity: springOpacity,
            scale: springScale 
          }}
        >
          {/* Stage Indicator */}
          <motion.div 
            className="mb-8 flex justify-center space-x-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            {videoStages.map((_, index) => (
              <div
                key={index}
                className={`w-12 h-1 rounded-full transition-all duration-500 ${
                  index === currentStage 
                    ? 'bg-[#C79500] shadow-lg shadow-[#C79500]/50' 
                    : 'bg-white/30'
                }`}
              />
            ))}
          </motion.div>

          {/* Dynamic Title */}
          <motion.h1 
            className="text-6xl md:text-8xl font-light tracking-tight mb-6"
            key={currentVideoStage.title}
            initial={{ opacity: 0, y: 30, rotateX: -10 }}
            animate={{ opacity: 1, y: 0, rotateX: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            style={{
              textShadow: '0 4px 20px rgba(0,0,0,0.5)'
            }}
          >
            {currentVideoStage.title}
            <motion.span 
              className="block text-[#C79500] mt-4"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              {currentVideoStage.subtitle}
            </motion.span>
          </motion.h1>

          {/* Location-Aware Subtitle */}
          <motion.p 
            className="text-xl md:text-2xl text-white/90 mb-12 max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            {locations[selectedLocation]?.legal || "Premium cannabis delivery in the DMV area"}
          </motion.p>

          {/* Interactive CTA */}
          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            {/* Address Input */}
            <div className="relative group">
              <input
                type="text"
                placeholder="Enter your address..."
                className="w-80 px-6 py-4 pr-14 bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl text-white placeholder:text-white/60 focus:outline-none focus:border-[#C79500] focus:bg-white/20 transition-all duration-300"
              />
              <button className="absolute right-2 top-1/2 -translate-y-1/2 p-2 text-[#C79500] hover:bg-[#C79500] hover:text-white rounded-xl transition-all duration-300">
                <Search className="w-6 h-6" />
              </button>
            </div>

            {/* Dynamic CTA Button */}
            <motion.button
              onClick={() => onCTAClick(currentVideoStage.id)}
              className="group relative px-8 py-4 bg-gradient-to-r from-[#C79500] to-[#D4A617] text-white font-medium rounded-2xl hover:from-[#B08400] hover:to-[#C79500] transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-[#C79500]/25"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="relative z-10 flex items-center">
                {currentVideoStage.cta}
                <Zap className="ml-2 w-5 h-5 group-hover:animate-pulse" />
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </motion.button>
          </motion.div>

          {/* Scroll Indicator */}
          {inView && (
            <motion.div 
              className="absolute bottom-8 left-1/2 -translate-x-1/2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2 }}
            >
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="flex flex-col items-center text-white/80"
              >
                <span className="text-sm mb-2">Scroll to explore</span>
                <ChevronDown className="w-6 h-6" />
              </motion.div>
            </motion.div>
          )}
        </motion.div>

        {/* Performance Metrics Overlay (Development) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="absolute top-4 right-4 z-30 bg-black/50 text-white p-2 rounded text-xs">
            <div>Stage: {currentStage + 1}/4</div>
            <div>Video: {currentVideoStage.id}</div>
            <div>Loaded: {isVideoLoaded ? 'Yes' : 'No'}</div>
          </div>
        )}
      </div>
    </div>
  );
}

// Utility function for throttling
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function(this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}