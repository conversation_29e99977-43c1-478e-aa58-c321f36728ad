"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  MapPin, 
  Truck, 
  Package, 
  CheckCircle, 
  Clock, 
  Phone, 
  MessageSquare,
  Navigation,
  User
} from "lucide-react"

interface DeliveryTrackerProps {
  orderId: string
  orderNumber: string
  estimatedDelivery?: Date
  driverName?: string
  driverPhone?: string
  currentStatus: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'OUT_FOR_DELIVERY' | 'DELIVERED'
}

interface DeliveryLocation {
  lat: number
  lng: number
  timestamp: Date
}

export function DeliveryTracker({ 
  orderId, 
  orderNumber, 
  estimatedDelivery, 
  driverName, 
  driverPhone, 
  currentStatus 
}: DeliveryTrackerProps) {
  const [progress, setProgress] = useState(0)
  const [driverLocation, setDriverLocation] = useState<DeliveryLocation | null>(null)
  const [eta, setEta] = useState<string>("")

  // Status configuration
  const statusConfig = {
    PENDING: { label: "Order Received", progress: 20, color: "bg-yellow-500" },
    CONFIRMED: { label: "Order Confirmed", progress: 40, color: "bg-blue-500" },
    PREPARING: { label: "Preparing Order", progress: 60, color: "bg-orange-500" },
    OUT_FOR_DELIVERY: { label: "Out for Delivery", progress: 80, color: "bg-purple-500" },
    DELIVERED: { label: "Delivered", progress: 100, color: "bg-green-500" }
  }

  const currentConfig = statusConfig[currentStatus]

  // Simulate real-time updates (in production, this would use Supabase Realtime)
  useEffect(() => {
    setProgress(currentConfig.progress)

    // Simulate driver location updates for out for delivery status
    if (currentStatus === 'OUT_FOR_DELIVERY') {
      const interval = setInterval(() => {
        // Simulate driver moving closer
        setDriverLocation({
          lat: 38.9072 + (Math.random() - 0.5) * 0.01,
          lng: -77.0369 + (Math.random() - 0.5) * 0.01,
          timestamp: new Date()
        })

        // Update ETA
        const minutes = Math.floor(Math.random() * 30) + 10
        setEta(`${minutes} minutes`)
      }, 5000)

      return () => clearInterval(interval)
    }
  }, [currentStatus, currentConfig.progress])

  const getStatusSteps = () => {
    const steps = [
      { key: 'PENDING', label: 'Order Received', icon: Package },
      { key: 'CONFIRMED', label: 'Confirmed', icon: CheckCircle },
      { key: 'PREPARING', label: 'Preparing', icon: Clock },
      { key: 'OUT_FOR_DELIVERY', label: 'Out for Delivery', icon: Truck },
      { key: 'DELIVERED', label: 'Delivered', icon: CheckCircle }
    ]

    const currentIndex = steps.findIndex(step => step.key === currentStatus)

    return steps.map((step, index) => ({
      ...step,
      completed: index <= currentIndex,
      current: index === currentIndex
    }))
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5 text-green-600" />
                Order #{orderNumber}
              </CardTitle>
              <CardDescription>
                Track your cannabis delivery in real-time
              </CardDescription>
            </div>
            <Badge 
              className={`${currentConfig.color} text-white`}
            >
              {currentConfig.label}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Progress Bar */}
            <div>
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {/* ETA */}
            {estimatedDelivery && (
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-gray-500" />
                <span>
                  Estimated delivery: {formatTime(estimatedDelivery)}
                  {eta && currentStatus === 'OUT_FOR_DELIVERY' && (
                    <span className="text-green-600 font-medium ml-2">
                      (ETA: {eta})
                    </span>
                  )}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Status Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Delivery Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {getStatusSteps().map((step, index) => {
              const Icon = step.icon
              return (
                <div key={step.key} className="flex items-center gap-4">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center
                    ${step.completed 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-gray-100 text-gray-400'
                    }
                    ${step.current ? 'ring-2 ring-green-500 ring-offset-2' : ''}
                  `}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <div className={`font-medium ${
                      step.completed ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                      {step.label}
                    </div>
                    {step.current && (
                      <div className="text-sm text-green-600">
                        In progress...
                      </div>
                    )}
                  </div>
                  {step.completed && (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Driver Info (when out for delivery) */}
      {currentStatus === 'OUT_FOR_DELIVERY' && driverName && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Your Driver
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">{driverName}</div>
                <div className="text-sm text-gray-600">
                  Professional delivery driver
                </div>
                {driverLocation && (
                  <div className="text-xs text-gray-500 mt-1">
                    Last updated: {formatTime(driverLocation.timestamp)}
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                {driverPhone && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={`tel:${driverPhone}`}>
                      <Phone className="h-4 w-4 mr-2" />
                      Call
                    </a>
                  </Button>
                )}
                <Button variant="outline" size="sm" asChild>
                  <a href={`sms:${driverPhone}`}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Text
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Live Map Placeholder */}
      {currentStatus === 'OUT_FOR_DELIVERY' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Live Tracking
            </CardTitle>
            <CardDescription>
              Follow your driver's location in real-time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
              <div className="text-center">
                <Navigation className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">Live map coming soon</p>
                <p className="text-sm text-gray-500">
                  Track your driver's location with AR mode
                </p>
                <Button variant="outline" className="mt-4" disabled>
                  <Navigation className="h-4 w-4 mr-2" />
                  AR Mode (Coming Soon)
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delivery Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Delivery Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
              <span>Please have your ID ready for age verification</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
              <span>Cash or card payment accepted</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
              <span>Delivery in discreet, unmarked packaging</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
              <span>Driver will call/text upon arrival</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
