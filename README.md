# WeedNearMeDC - Cannabis Delivery Platform

A comprehensive cannabis delivery platform for Washington DC, Maryland, and Virginia, built with Next.js 14, React 18, Tailwind CSS, and Supabase.

## Features

### Core Functionality
- ✅ Age Gate - 21+ verification with session storage
- ✅ Product Catalog - Products across categories (flower, edibles, vapes, concentrates, wellness)
- ✅ Shopping Cart - Add/remove items, persistent localStorage
- ✅ Location System - DC, MD, VA with different delivery times/minimums
- ✅ Product Pages - Detailed product views with specs, effects, reviews
- ✅ Search & Filters - By category, brand, effects, price range, ratings
- ✅ Responsive Design - Mobile-friendly navigation and layout
- ✅ Trust Signals - Licensed, same-day delivery, lab tested badges

### Advanced Features
- ✅ Authentication System - User accounts, login, registration
- ✅ Real-time Delivery Tracking - Track orders in real-time
- ✅ Payment Processing - Secure checkout flow
- ✅ Admin Dashboard - Product management, order processing, analytics
- ✅ SEO Optimization - Schema markup, XML sitemap, robots.txt

## Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **React 18** - UI library
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Reusable UI components
- **TypeScript** - Type-safe JavaScript

### Backend
- **Supabase** - Backend-as-a-Service (Auth, Database, Storage)
- **GraphQL** - API query language
- **tRPC** - End-to-end typesafe APIs

### Infrastructure
- **Vercel** - Deployment platform
- **GitHub** - Version control

## Project Structure

```
weednearmedc/
├── src/
│   ├── app/                  # Next.js App Router
│   │   ├── admin/            # Admin dashboard
│   │   ├── auth/             # Authentication pages
│   │   ├── cart/             # Shopping cart
│   │   ├── checkout/         # Checkout flow
│   │   ├── md/               # Maryland shop
│   │   ├── track/            # Order tracking
│   │   ├── api/              # API routes
│   │   ├── layout.tsx        # Root layout
│   │   └── page.tsx          # Home page
│   ├── components/           # React components
│   │   ├── auth/             # Auth components
│   │   ├── cannabis/         # Cannabis-specific components
│   │   ├── delivery/         # Delivery components
│   │   ├── layout/           # Layout components
│   │   ├── providers/        # Context providers
│   │   ├── seo/              # SEO components
│   │   └── ui/               # UI components
│   ├── constants/            # App constants
│   ├── data/                 # Mock data
│   ├── lib/                  # Utility functions
│   │   ├── graphql/          # GraphQL schema and resolvers
│   │   ├── supabase/         # Supabase client
│   │   └── trpc/             # tRPC setup
│   └── types/                # TypeScript types
├── prisma/                   # Prisma ORM
├── public/                   # Static assets
├── middleware.ts             # Next.js middleware
└── package.json              # Dependencies
```

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/weednearmedc.git
cd weednearmedc
```

2. Install dependencies
```bash
npm install
# or
yarn install
```

3. Set up environment variables
```
# Create a .env.local file with the following variables
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

4. Run the development server
```bash
npm run dev
# or
yarn dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Key Pages

- **Home**: `/` - Landing page with location selection
- **Age Gate**: `/age-gate` - Age verification
- **Maryland Shop**: `/md` - Main product catalog for Maryland
- **Product Detail**: `/md/products/[category]/[slug]` - Detailed product page
- **Cart**: `/cart` - Shopping cart
- **Checkout**: `/checkout` - Checkout process
- **Order Tracking**: `/track/[orderId]` - Real-time order tracking
- **Authentication**: `/auth/login` and `/auth/register` - User authentication
- **Account**: `/account` - User account management
- **Admin Dashboard**: `/admin` - Admin interface

## Compliance Considerations

- Age verification required (21+)
- State-specific regulations for DC, MD, VA
- Lab testing information displayed
- Responsible consumption messaging
- Secure payment processing

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [React](https://reactjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)
- [Supabase](https://supabase.io/)
- [Vercel](https://vercel.com/)
