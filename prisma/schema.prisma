// WeedNearMeDC Cannabis Delivery Platform Schema
// Production-ready schema for DC/MD/VA cannabis delivery

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management & Authentication
model User {
  id        String   @id @default(uuid()) @db.Uuid
  email     String   @unique
  password  String?  // Optional for OAuth users
  firstName String?
  lastName  String?
  phone     String?
  dateOfBirth DateTime?

  // Age verification
  ageVerified <PERSON>olean @default(false)
  idVerified  Boolean @default(false)
  idImageUrl  String?

  // Location & Preferences
  state       String? // DC, MD, VA
  city        String?
  zipCode     String?
  address     String?

  // Account status
  isActive    Boolean @default(true)
  role        UserRole @default(CUSTOMER)

  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLoginAt DateTime?

  // Relations
  orders      Order[]
  reviews     Review[]
  loyalty     Loyalty?
  cart        CartItem[]
  favorites   Favorite[]

  @@map("users")
}

enum UserRole {
  CUSTOMER
  DRIVER
  ADMIN
  SUPER_ADMIN
}

// Product Catalog
model Product {
  id          Int      @id @default(autoincrement())
  name        String
  slug        String   @unique
  description String?
  category    ProductCategory
  subcategory String?

  // Cannabis-specific fields
  thcContent  String?  // "20-25%"
  cbdContent  String?  // "0-1%"
  strain      String?  // "Indica", "Sativa", "Hybrid"
  effects     String[] // ["relaxing", "euphoric", "creative"]
  flavors     String[] // ["citrus", "earthy", "sweet"]

  // Pricing & Inventory
  price       Int      // Price in cents
  compareAtPrice Int?  // Original price for sales
  weight      String?  // "1g", "3.5g", "7g", "14g", "28g"
  inStock     Boolean  @default(true)
  stockCount  Int      @default(0)

  // SEO & Marketing
  metaTitle       String?
  metaDescription String?
  featured        Boolean @default(false)
  onSale          Boolean @default(false)

  // Media
  images      String[] // Array of image URLs
  videoUrl    String?

  // Ratings & Reviews
  rating      Float    @default(0)
  reviewCount Int      @default(0)

  // Compliance
  labTested   Boolean  @default(false)
  labResults  String?  // URL to lab results

  // State availability
  availableInDC Boolean @default(false)
  availableInMD Boolean @default(true)  // Primary market
  availableInVA Boolean @default(false)

  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  orderItems  OrderItem[]
  reviews     Review[]
  cartItems   CartItem[]
  favorites   Favorite[]

  @@map("products")
}

enum ProductCategory {
  FLOWER
  EDIBLES
  VAPES
  CONCENTRATES
  TOPICALS
  ACCESSORIES
  PRE_ROLLS
  TINCTURES
}

// Shopping Cart
model CartItem {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  productId Int
  quantity  Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("cart_items")
}

// User Favorites
model Favorite {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  productId Int
  createdAt DateTime @default(now())

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("favorites")
}

// Order Management
model Order {
  id          Int         @id @default(autoincrement())
  userId      String      @db.Uuid
  orderNumber String      @unique

  // Order details
  status      OrderStatus @default(PENDING)
  total       Int         // Total in cents
  subtotal    Int         // Subtotal in cents
  tax         Int         // Tax in cents
  tip         Int         @default(0) // Tip in cents
  deliveryFee Int         @default(0) // Delivery fee in cents

  // Delivery information
  deliveryAddress String
  deliveryCity    String
  deliveryState   String
  deliveryZip     String
  deliveryNotes   String?

  // Driver assignment
  driverId        String?     @db.Uuid
  estimatedDelivery DateTime?
  actualDelivery    DateTime?

  // Payment
  paymentMethod   String?
  paymentStatus   PaymentStatus @default(PENDING)

  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User @relation(fields: [userId], references: [id])
  items           OrderItem[]

  @@map("orders")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PREPARING
  OUT_FOR_DELIVERY
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
}

// Order Items
model OrderItem {
  id        Int @id @default(autoincrement())
  orderId   Int
  productId Int
  quantity  Int
  price     Int // Price at time of order in cents

  // Relations
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Reviews & Ratings
model Review {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  productId Int
  rating    Int      // 1-5 stars
  title     String?
  body      String?
  verified  Boolean  @default(false) // Verified buyer
  helpful   Int      @default(0)     // Helpful votes

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId]) // One review per user per product
  @@map("reviews")
}

// Loyalty & Gamification
model Loyalty {
  userId    String   @id @db.Uuid
  points    Int      @default(0)
  orders    Int      @default(0)
  tier      LoyaltyTier @default(BRONZE)
  badges    String[] // Array of earned badge IDs

  // CannaDex progress
  strainsDiscovered Int @default(0)
  categoriesExplored Int @default(0)

  // Virtual greenhouse
  greenhouseLevel   Int @default(1)
  plantsOwned       String[] // Array of plant IDs

  // Referrals
  referralCode      String   @unique
  referralsCount    Int      @default(0)

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("loyalty")
}

enum LoyaltyTier {
  BRONZE
  SILVER
  GOLD
  PLATINUM
  DIAMOND
}
